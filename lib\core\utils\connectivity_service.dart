import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

enum NetworkStatus { online, offline }

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  final StreamController<NetworkStatus> _networkStatusController = StreamController<NetworkStatus>.broadcast();

  Stream<NetworkStatus> get networkStatusStream => _networkStatusController.stream;

  ConnectivityService() {
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      _networkStatusController.add(_getNetworkStatus(result));
    });
  }

  Future<NetworkStatus> get initialStatus async {
    ConnectivityResult result = await _connectivity.checkConnectivity();
    return _getNetworkStatus(result);
  }

  NetworkStatus _getNetworkStatus(ConnectivityResult result) {
    return result == ConnectivityResult.none ? NetworkStatus.offline : NetworkStatus.online;
  }

  void dispose() {
    _networkStatusController.close();
  }
}
