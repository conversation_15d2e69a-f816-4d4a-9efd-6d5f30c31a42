import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/bottom_nav_app_bar.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';
import 'package:medicine_shop/features/products/presentation/providers/product_provider.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/admin/presentation/widgets/admin_stat_card.dart';
import 'package:medicine_shop/features/admin/presentation/widgets/admin_chart.dart';

import 'package:intl/intl.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  bool _isLoading = false;
  int _totalProducts = 0;
  int _totalOrders = 0;
  int _pendingOrders = 0;
  double _totalRevenue = 0;

  @override
  void initState() {
    super.initState();

    // Load dashboard data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productProvider = Provider.of<ProductProvider>(context, listen: false);
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

      // Load products
      await productProvider.loadProducts();

      // Load all orders for admin
      await orderProvider.loadAllOrders();

      // Load all prescriptions for admin
      await prescriptionProvider.loadAllPrescriptions();

      // Calculate dashboard metrics
      setState(() {
        _totalProducts = productProvider.products.length;
        _totalOrders = orderProvider.orders.length;
        _pendingOrders = orderProvider.orders.where((order) => order.isPending).length;
        _totalRevenue = orderProvider.orders.fold(0, (sum, order) => sum + order.totalAmount);
      });
    } catch (e) {
      // Handle error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BottomNavAppBar(
        title: 'Admin Dashboard',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
            tooltip: 'Logout',
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isAuthenticated) {
            return const Center(
              child: Text('Please login to access admin dashboard'),
            );
          }

          if (!authProvider.isAdmin) {
            return const Center(
              child: Text('You do not have permission to access this page'),
            );
          }

          return _buildDashboardTab();
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/admin/add-product');
        },
        backgroundColor: ScreenConstants.primaryGradientStart,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildDashboardTab() {
    if (_isLoading) {
      return const LoadingIndicator(message: 'Loading dashboard data...');
    }

    // Generate sample data for charts
    final salesData = [
      ChartData(label: 'Mon', value: 1200),
      ChartData(label: 'Tue', value: 1800),
      ChartData(label: 'Wed', value: 1400),
      ChartData(label: 'Thu', value: 2200),
      ChartData(label: 'Fri', value: 1600),
      ChartData(label: 'Sat', value: 2400),
      ChartData(label: 'Sun', value: 1900),
    ];

    final ordersData = [
      ChartData(label: 'Mon', value: 12),
      ChartData(label: 'Tue', value: 18),
      ChartData(label: 'Wed', value: 14),
      ChartData(label: 'Thu', value: 22),
      ChartData(label: 'Fri', value: 16),
      ChartData(label: 'Sat', value: 24),
      ChartData(label: 'Sun', value: 19),
    ];

    // Calculate percentage change for trends
    final lastMonthRevenue = _totalRevenue * 0.85; // Simulated previous month data
    final revenueChange = ((_totalRevenue - lastMonthRevenue) / lastMonthRevenue) * 100;
    final isRevenuePositive = revenueChange >= 0;

    final lastMonthOrders = _totalOrders - 5; // Simulated previous month data
    final ordersChange = (((_totalOrders - lastMonthOrders) / lastMonthOrders) * 100);
    final isOrdersPositive = ordersChange >= 0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ScreenConstants.primaryGradientStart.withAlpha(25),
                  ScreenConstants.primaryGradientEnd.withAlpha(15),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Welcome to Admin Dashboard',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Today is ${DateFormat('EEEE, MMMM d, yyyy').format(DateTime.now())}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'You have $_pendingOrders pending orders to process',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.admin_panel_settings,
                  size: 80,
                  color: ScreenConstants.primaryGradientStart,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Stats Cards
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              AdminStatCard(
                title: 'Total Products',
                value: _totalProducts.toString(),
                icon: Icons.inventory,
                color: Colors.blue,
                onTap: () => Navigator.pushNamed(context, '/admin/add-product'),
                subtitle: 'Available in store',
              ),
              AdminStatCard(
                title: 'Total Orders',
                value: _totalOrders.toString(),
                icon: Icons.shopping_bag,
                color: Colors.green,
                onTap: () => Navigator.pushNamed(context, '/admin/orders'),
                showTrend: true,
                isPositiveTrend: isOrdersPositive,
                trendValue: '${ordersChange.toStringAsFixed(1)}%',
              ),
              AdminStatCard(
                title: 'Pending Orders',
                value: _pendingOrders.toString(),
                icon: Icons.pending_actions,
                color: Colors.orange,
                onTap: () => Navigator.pushNamed(context, '/admin/orders'),
                subtitle: 'Needs attention',
              ),
              AdminStatCard(
                title: 'Total Revenue',
                value: '₹${_totalRevenue.toStringAsFixed(2)}',
                icon: Icons.attach_money,
                color: Colors.purple,
                showTrend: true,
                isPositiveTrend: isRevenuePositive,
                trendValue: '${revenueChange.toStringAsFixed(1)}%',
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Charts
          AdminChart(
            title: 'Weekly Sales',
            subtitle: 'Last 7 days',
            data: salesData,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          AdminChart(
            title: 'Weekly Orders',
            subtitle: 'Last 7 days',
            data: ordersData,
            color: Colors.green,
          ),
          const SizedBox(height: 24),

          // Quick Actions
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Add New Product',
                  onPressed: () {
                    Navigator.pushNamed(context, '/admin/add-product');
                  },
                  type: ButtonType.primary,
                  icon: Icons.add_circle,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomButton(
                  text: 'Unassigned Orders',
                  onPressed: () {
                    Navigator.pushNamed(context, '/admin/unassigned-orders');
                  },
                  type: ButtonType.secondary,
                  icon: Icons.pending,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Upload Prescription',
                  onPressed: () {
                    Navigator.pushNamed(context, '/admin/upload-prescription');
                  },
                  type: ButtonType.secondary,
                  icon: Icons.upload_file,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomButton(
                  text: 'Order History',
                  onPressed: () {
                    Navigator.pushNamed(context, '/admin/order-history');
                  },
                  type: ButtonType.secondary,
                  icon: Icons.history,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Recent Activity
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Recent Orders',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/admin/orders');
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Consumer<OrderProvider>(
            builder: (context, orderProvider, child) {
              final recentOrders = orderProvider.orders.take(5).toList();

              if (recentOrders.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      const Icon(Icons.shopping_bag_outlined, size: 64, color: Colors.grey),
                      const SizedBox(height: 16),
                      const Text('No recent orders'),
                      const SizedBox(height: 16),
                      CustomButton(
                        text: 'Refresh',
                        onPressed: () {
                          orderProvider.loadAllOrders();
                        },
                        type: ButtonType.secondary,
                        icon: Icons.refresh,
                        isFullWidth: false,
                      ),
                    ],
                  ),
                );
              }

              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: recentOrders.length,
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final order = recentOrders[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: _getStatusColor(order.orderStatus),
                        child: const Icon(Icons.shopping_bag, color: Colors.white),
                      ),
                      title: Text('Order #${order.id.substring(0, 8)}'),
                      subtitle: Text(
                        'Status: ${order.orderStatus} | ${DateFormat('MMM d, yyyy').format(order.orderDate)}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '₹${order.totalAmount.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ScreenConstants.primaryGradientStart,
                            ),
                          ),
                          Text(
                            '${order.cartItems.length} items',
                            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                      onTap: () {
                        // Navigate to order details without pre-loading
                        Navigator.pushNamed(context, '/admin/order-details', arguments: order.id);
                      },
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Keeping this method for potential future use
  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // First logout
              Provider.of<AuthProvider>(context, listen: false).logout();
              // Then navigate to login screen
              Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'processing':
        return Colors.purple;
      case 'shipped':
        return Colors.indigo;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'assigned':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
