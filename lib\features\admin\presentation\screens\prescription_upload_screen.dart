import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/custom_text_field.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/admin/presentation/providers/user_provider.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';

class PrescriptionUploadScreen extends StatefulWidget {
  final String? userId; // Optional: If provided, will pre-select this user

  const PrescriptionUploadScreen({
    super.key,
    this.userId,
  });

  @override
  State<PrescriptionUploadScreen> createState() => _PrescriptionUploadScreenState();
}

class _PrescriptionUploadScreenState extends State<PrescriptionUploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  File? _imageFile;
  String? _selectedUserId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedUserId = widget.userId;

    // Load users if we're in admin mode
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.isAdmin && widget.userId == null) {
        Provider.of<UserProvider>(context, listen: false).loadAllUsers();
      }
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _imageFile = File(pickedFile.path);
      });
    }
  }

  Future<void> _uploadPrescription() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_imageFile == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a prescription image'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
        return;
      }

      if (_selectedUserId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a user'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

        if (authProvider.user != null) {
          final success = await prescriptionProvider.uploadPrescription(
            _imageFile!,
            authProvider.user!,
            authProvider.user!.id, // Admin ID
            _notesController.text,
          );

          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Prescription uploaded successfully!'),
                backgroundColor: AppTheme.primaryColor,
              ),
            );

            // Clear form
            setState(() {
              _imageFile = null;
              _notesController.clear();
            });

            // Navigate back
            Navigator.pop(context);
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(prescriptionProvider.errorMessage ?? 'Failed to upload prescription'),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Upload Prescription',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Prescription Image
              Center(
                child: GestureDetector(
                  onTap: _isLoading ? null : _pickImage,
                  child: Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey),
                    ),
                    child: _imageFile != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.file(
                              _imageFile!,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add_a_photo,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Tap to add prescription image',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // User Selection (if not pre-selected)
              if (widget.userId == null) ...[
                const Text(
                  'Select User',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Consumer<UserProvider>(
                  builder: (context, userProvider, child) {
                    if (userProvider.status == UserStatus.loading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (userProvider.status == UserStatus.error) {
                      return Center(
                        child: Text(
                          userProvider.errorMessage ?? 'Failed to load users',
                          style: const TextStyle(color: AppTheme.errorColor),
                        ),
                      );
                    }

                    final users = userProvider.users;

                    if (users.isEmpty) {
                      return const Center(
                        child: Text('No users found'),
                      );
                    }

                    return DropdownButtonFormField<String>(
                      decoration: AppTheme.inputDecoration('User'),
                      value: _selectedUserId,
                      items: users.map((user) => DropdownMenuItem(
                        value: user.id,
                        child: Text('${user.userName} (${user.email})'),
                      )).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedUserId = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a user';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Notes
              CustomTextField(
                label: 'Notes',
                hint: 'Enter any notes about the prescription',
                controller: _notesController,
                prefixIcon: Icons.note,
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // Upload Button
              CustomButton(
                text: 'Upload Prescription',
                onPressed: _isLoading ? () {} : _uploadPrescription,
                isLoading: _isLoading,
                type: ButtonType.primary,
                icon: Icons.upload_file,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
