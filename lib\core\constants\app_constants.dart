import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConstants {
  // API URLs - Updated to match the backend
  // static String get baseUrl => dotenv.env['API_BASE_URL'] ?? 'http://localhost:5000/api';
  // static String get socketUrl => dotenv.env['SOCKET_URL'] ?? 'http://localhost:5000';
  // static String get uploadsUrl => dotenv.env['UPLOADS_URL'] ?? 'http://localhost:5000/uploads';
  // static String get directUploadUrl => dotenv.env['DIRECT_UPLOAD_URL'] ?? 'http://localhost:5000/upload';
  // static String get cloudinaryUrl => 'https://res.cloudinary.com/duwpnrt2k/image/upload';
  
  static String get baseUrl => dotenv.env['API_BASE_URL'] ?? 'https://mern-ecommerce-2024-copy.onrender.com/api';
  static String get socketUrl => dotenv.env['SOCKET_URL'] ?? 'https://mern-ecommerce-2024-copy.onrender.com';
  static String get uploadsUrl => dotenv.env['UPLOADS_URL'] ?? 'https://mern-ecommerce-2024-copy.onrender.com/uploads';
  static String get directUploadUrl => dotenv.env['DIRECT_UPLOAD_URL'] ?? 'https://mern-ecommerce-2024-copy.onrender.com/upload';
  static String get cloudinaryUrl => 'https://res.cloudinary.com/duwpnrt2k/image/upload';

  // For cookie-based authentication
  static const bool withCredentials = true;

  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String shopProductsEndpoint = '/shop/products';
  static const String shopCartEndpoint = '/shop/cart';
  static const String shopAddressEndpoint = '/shop/address';
  static const String shopOrderEndpoint = '/shop/order';
  static const String shopSearchEndpoint = '/shop/search';
  static const String shopReviewEndpoint = '/shop/review';

  static const String adminProductsEndpoint = '/admin/products';
  static const String adminOrdersEndpoint = '/admin/orders';
  static const String adminPrescriptionsEndpoint = '/admin/prescriptions';
  static const String prescriptionEndpoint = '/shop/prescription';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';

  // Roles
  static const String userRole = 'user';
  static const String adminRole = 'admin';

  // Order Status
  static const String orderStatusPending = 'pending';
  static const String orderStatusConfirmed = 'confirmed';
  static const String orderStatusProcessing = 'processing';
  static const String orderStatusShipped = 'shipped';
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';

  // Payment Status
  static const String paymentStatusPending = 'pending';
  static const String paymentStatusPaid = 'paid';
  static const String paymentStatusFailed = 'failed';

  // Payment Methods
  static const String paymentMethodCOD = 'cod';
  static const String paymentMethodRazorpay = 'razorpay';

  // Prescription Status
  static const String prescriptionStatusPending = 'pending';
  static const String prescriptionStatusApproved = 'approved';
  static const String prescriptionStatusRejected = 'rejected';
}
