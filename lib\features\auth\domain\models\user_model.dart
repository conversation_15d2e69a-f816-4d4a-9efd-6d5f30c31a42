class UserModel {
  final String id;
  final String userName;
  final String? phoneNumber; // Made optional to match backend
  final String email; // Made required to match backend
  final String role;

  UserModel({
    required this.id,
    required this.userName,
    this.phoneNumber,
    required this.email,
    required this.role,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    print('Creating UserModel from JSON: $json');

    // Extract id, handling both 'id' and '_id' formats
    String userId = '';
    if (json.containsKey('id')) {
      userId = json['id'].toString();
    } else if (json.containsKey('_id')) {
      userId = json['_id'].toString();
    }

    // Handle the case where the backend returns a nested user object
    Map<String, dynamic> userData = json;
    if (json.containsKey('user')) {
      userData = json['user'];

      // Re-check for id in the user object
      if (userId.isEmpty) {
        if (userData.containsKey('id')) {
          userId = userData['id'].toString();
        } else if (userData.containsKey('_id')) {
          userId = userData['_id'].toString();
        }
      }
    }

    return UserModel(
      id: userId,
      userName: userData['userName'] ?? '',
      phoneNumber: userData['phoneNumber'],
      email: userData['email'] ?? '',
      role: userData['role'] ?? 'user',
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'userName': userName,
      'email': email,
      'role': role,
    };

    // Only include phoneNumber if it's not null
    if (phoneNumber != null) {
      data['phoneNumber'] = phoneNumber;
    }

    return data;
  }

  // For registration payload
  Map<String, dynamic> toRegistrationJson() {
    final Map<String, dynamic> data = {
      'userName': userName,
      'email': email,
      'role': role,
    };

    // Only include phoneNumber if it's not null
    if (phoneNumber != null) {
      data['phoneNumber'] = phoneNumber;
    }

    return data;
  }

  bool get isAdmin => role == 'admin';
}
