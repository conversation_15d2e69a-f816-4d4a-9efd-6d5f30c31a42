import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class AdminOrderDetailsScreen extends StatefulWidget {
  final String orderId;

  const AdminOrderDetailsScreen({
    super.key,
    required this.orderId,
  });

  @override
  State<AdminOrderDetailsScreen> createState() => _AdminOrderDetailsScreenState();
}

class _AdminOrderDetailsScreenState extends State<AdminOrderDetailsScreen> {
  bool _isUpdating = false;
  Timer? _refreshTimer;
  final _statusController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load order details only if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);

      // Only load if the selected order is null or has a different ID
      if (orderProvider.selectedOrder == null || orderProvider.selectedOrder!.id != widget.orderId) {
        orderProvider.loadOrderDetails(widget.orderId);
      }

      // Set up a timer to refresh the order details every 60 seconds
      _refreshTimer = Timer.periodic(const Duration(seconds: 60), (_) {
        if (mounted) {
          // Only refresh if the user is still on this screen
          // This avoids unnecessary API calls
          orderProvider.loadOrderDetails(widget.orderId);
        }
      });
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _statusController.dispose();
    super.dispose();
  }

  void _updateOrderStatus(String status) {
    setState(() {
      _isUpdating = true;
    });

    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    orderProvider.updateOrderStatus(widget.orderId, status).then((updatedOrder) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Order status updated successfully'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    }).catchError((error) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(error.toString()),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }).whenComplete(() {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    });
  }

  void _showCustomStatusDialog() {
    _statusController.clear();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: TextField(
          controller: _statusController,
          decoration: const InputDecoration(
            labelText: 'Status',
            hintText: 'Enter custom status',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final status = _statusController.text.trim();
              if (status.isNotEmpty) {
                Navigator.pop(context);
                _updateOrderStatus(status);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Order Details',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              Provider.of<OrderProvider>(context, listen: false).loadOrderDetails(widget.orderId);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Order details refreshed'),
                  duration: Duration(seconds: 1),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<OrderProvider>(
        builder: (context, orderProvider, child) {
          if (orderProvider.status == OrderStatus.loading) {
            return const LoadingIndicator(message: 'Loading order details...');
          }

          if (orderProvider.status == OrderStatus.error) {
            return CustomErrorWidget(
              message: orderProvider.errorMessage ?? 'Failed to load order details',
              onRetry: () => orderProvider.loadOrderDetails(widget.orderId),
            );
          }

          final order = orderProvider.selectedOrder;

          if (order == null) {
            return const Center(
              child: Text('Order not found'),
            );
          }

          return Column(
            children: [
              // Order header with status
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Order #${order.id.substring(0, 8)}',
                                    style: const TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    DateFormat('MMMM d, yyyy - h:mm a').format(order.orderDate),
                                    style: TextStyle(
                                      color: Colors.white.withAlpha(200),
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                              _buildStatusBadge(order.orderStatus),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Total Amount',
                                    style: TextStyle(
                                      color: Colors.white.withAlpha(180),
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '₹${order.totalAmount.toStringAsFixed(0)}',
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(30),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  '${order.cartItems.length} items',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Order details content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [

                      // Order Items
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Order Items',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ...order.cartItems.map((item) => Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Row(
                                  children: [
                                    // Product Image
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        item.image.isNotEmpty && item.image.startsWith('http')
                                            ? item.image
                                            : 'https://via.placeholder.com/60',
                                        width: 60,
                                        height: 60,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) => Container(
                                          width: 60,
                                          height: 60,
                                          color: Colors.grey[200],
                                          child: const Icon(Icons.image_not_supported),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),

                                    // Product Details
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            item.title,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'Quantity: ${item.quantity}',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Price
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          '₹${item.finalPrice.toStringAsFixed(0)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        Text(
                                          'Total: ₹${(item.finalPrice * item.quantity).toStringAsFixed(0)}',
                                          style: const TextStyle(
                                            color: AppTheme.primaryColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                            ],
                          ),
                        )),
                        const Divider(),

                        // Order Summary
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Subtotal:'),
                            Text('₹${order.totalAmount.toStringAsFixed(0)}'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Shipping:'),
                            Text('FREE'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              '₹${order.totalAmount.toStringAsFixed(0)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Customer and Shipping Information
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.person, color: AppTheme.primaryColor),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Customer Information',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.grey.shade200),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.person_outline, color: Colors.grey),
                                        const SizedBox(width: 8),
                                        Expanded(child: Text('Customer ID: ${order.userId}')),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      children: [
                                        const Icon(Icons.phone, color: Colors.grey),
                                        const SizedBox(width: 8),
                                        Text('Phone: ${order.addressInfo.phone}'),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const Icon(Icons.location_on, color: Colors.grey),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text('Address: ${order.addressInfo.address}, ${order.addressInfo.city}, ${order.addressInfo.pincode}'),
                                        ),
                                      ],
                                    ),
                                    if (order.addressInfo.notes.isNotEmpty) ...[
                                      const SizedBox(height: 12),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const Icon(Icons.note, color: Colors.grey),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text('Notes: ${order.addressInfo.notes}'),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),



                      // Payment Information
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.payment, color: AppTheme.primaryColor),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Payment Information',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.grey.shade200),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.payment, color: Colors.grey),
                                        const SizedBox(width: 8),
                                        Text('Payment Method: ${order.paymentMethod}'),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      children: [
                                        const Icon(Icons.receipt, color: Colors.grey),
                                        const SizedBox(width: 8),
                                        Text('Payment Status: ${order.paymentStatus}'),
                                      ],
                                    ),
                                    if (order.paymentId != null && order.paymentId!.isNotEmpty) ...[
                                      const SizedBox(height: 12),
                                      Row(
                                        children: [
                                          const Icon(Icons.confirmation_number, color: Colors.grey),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text('Payment ID: ${order.paymentId}'),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Update Status
                      if (!order.isDelivered && !order.isCancelled)
                        Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.update, color: AppTheme.primaryColor),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Update Order Status',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                // Status timeline
                                Container(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Order Timeline',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      _buildStatusTimeline(order.orderStatus),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Custom status button
                                const SizedBox(height: 16),
                                OutlinedButton.icon(
                                  onPressed: _isUpdating ? null : _showCustomStatusDialog,
                                  icon: const Icon(Icons.edit),
                                  label: const Text('Set Custom Status'),
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;

    switch (status.toLowerCase()) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'confirmed':
        color = Colors.blue;
        break;
      case 'processing':
        color = Colors.purple;
        break;
      case 'shipped':
        color = Colors.indigo;
        break;
      case 'delivered':
        color = Colors.green;
        break;
      case 'cancelled':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }



  Widget _buildStatusTimeline(String currentStatus) {
    final statuses = [
      'pending',
      'confirmed',
      'processing',
      'shipped',
      'delivered',
    ];

    final statusLabels = {
      'pending': 'Order Placed',
      'confirmed': 'Confirmed',
      'processing': 'Processing',
      'shipped': 'Shipped',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled',
    };

    // Add a hint text to explain the clickable timeline
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: Text(
            'Click on any status to update the order',
            style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic, color: Colors.grey),
          ),
        ),
        if (currentStatus.toLowerCase() == 'cancelled')
          Column(
            children: [
              _buildTimelineItem(
                'Order Placed',
                true,
                Colors.orange,
                status: 'pending',
              ),
              _buildTimelineItem(
                'Cancelled',
                true,
                Colors.red,
                isLast: true,
                status: 'cancelled',
              ),
            ],
          )
        else
          Column(
            children: [
              // Regular status timeline
              ...List.generate(statuses.length, (index) {
                final status = statuses[index];
                final currentIndex = statuses.indexOf(currentStatus.toLowerCase());
                final isCompleted = index <= currentIndex;
                final color = _getStatusColor(status);
                final isLast = index == statuses.length - 1;

                return _buildTimelineItem(
                  statusLabels[status] ?? status,
                  isCompleted,
                  color,
                  isLast: isLast,
                  status: status,
                );
              }),

              // Add a divider before the cancel option
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Divider(height: 1),
              ),

              // Cancel option (always available)
              _buildCancelOption(),
            ],
          ),
      ],
    );
  }

  Widget _buildCancelOption() {
    return InkWell(
      onTap: _isUpdating ? null : () {
        _showCancelConfirmationDialog();
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(50),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.red, width: 2),
              ),
              child: const Icon(Icons.close, size: 16, color: Colors.red),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Text(
                'Cancel Order',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order?'),
        content: const Text(
          'Are you sure you want to cancel this order? This action cannot be undone.',
          style: TextStyle(color: Colors.red),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No, Keep Order'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _updateOrderStatus('cancelled');
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Yes, Cancel Order'),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(String label, bool isCompleted, Color color, {bool isLast = false, String? status, bool isClickable = true}) {
    final row = Row(
      children: [
        // Status indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted ? color : Colors.grey[300],
                shape: BoxShape.circle,
                border: Border.all(
                  color: isCompleted ? color : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isCompleted
                  ? const Icon(Icons.check, size: 16, color: Colors.white)
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 30,
                color: isCompleted ? color : Colors.grey[300],
              ),
          ],
        ),
        const SizedBox(width: 16),
        // Status label
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontWeight: isCompleted ? FontWeight.bold : FontWeight.normal,
              color: isCompleted ? color : Colors.grey[600],
            ),
          ),
        ),
        // Date (would be populated with actual data in a real app)
        if (isCompleted)
          Text(
            DateFormat('MMM d, h:mm a').format(DateTime.now()),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
      ],
    );

    // If status is provided and the item is clickable, make it tappable to update status
    if (status != null && isClickable && !_isUpdating) {
      return InkWell(
        onTap: () {
          _showStatusConfirmationDialog(status, label);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: row,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: row,
    );
  }

  void _showStatusConfirmationDialog(String status, String label) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update to $label?'),
        content: Text('Are you sure you want to update the order status to "$label"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _updateOrderStatus(status);
            },
            style: TextButton.styleFrom(
              foregroundColor: _getStatusColor(status),
            ),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'processing':
        return Colors.purple;
      case 'shipped':
        return Colors.indigo;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
