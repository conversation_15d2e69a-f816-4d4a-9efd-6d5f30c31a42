import 'package:medicine_shop/core/utils/number_utils.dart';
import 'package:medicine_shop/features/cart/domain/models/cart_model.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';

class OrderAddressInfo {
  final String addressId;
  final String address;
  final String city;
  final String pincode;
  final String phone;
  final String notes;

  OrderAddressInfo({
    required this.addressId,
    required this.address,
    required this.city,
    required this.pincode,
    required this.phone,
    required this.notes,
  });

  factory OrderAddressInfo.fromJson(Map<String, dynamic> json) {
    return OrderAddressInfo(
      addressId: json['addressId'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      pincode: json['pincode'] ?? '',
      phone: json['phone'] ?? '',
      notes: json['notes'] ?? '',
    );
  }

  factory OrderAddressInfo.fromAddressModel(AddressModel address) {
    return OrderAddressInfo(
      addressId: address.id,
      address: address.address,
      city: address.city,
      pincode: address.pincode,
      phone: address.phone,
      notes: address.notes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'addressId': addressId,
      'address': address,
      'city': city,
      'pincode': pincode,
      'phone': phone,
      'notes': notes,
    };
  }
}

class OrderModel {
  final String id;
  final String userId;
  final String? cartId;
  final List<CartItemModel> cartItems;
  final OrderAddressInfo addressInfo;
  final String? assignedTo;
  final String orderStatus;
  final String paymentMethod;
  final String paymentStatus;
  final double totalAmount;
  final DateTime orderDate;
  final DateTime? orderUpdateDate;
  final String? paymentId;
  final String? payerId;

  OrderModel({
    required this.id,
    required this.userId,
    this.cartId,
    required this.cartItems,
    required this.addressInfo,
    this.assignedTo,
    required this.orderStatus,
    required this.paymentMethod,
    required this.paymentStatus,
    required this.totalAmount,
    required this.orderDate,
    this.orderUpdateDate,
    this.paymentId,
    this.payerId,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      cartId: json['cartId'],
      cartItems: (json['cartItems'] as List<dynamic>?)
              ?.map((item) => CartItemModel.fromJson(item))
              .toList() ??
          [],
      addressInfo: OrderAddressInfo.fromJson(json['addressInfo'] ?? {}),
      assignedTo: json['assignedTo'] == null ? null : (json['assignedTo'] is Map ? json['assignedTo']['_id'] : json['assignedTo']),
      orderStatus: json['orderStatus'] ?? '',
      paymentMethod: json['paymentMethod'] ?? '',
      paymentStatus: json['paymentStatus'] ?? '',
      totalAmount: NumberUtils.parseDouble(json['totalAmount']),
      orderDate: json['orderDate'] != null
          ? DateTime.parse(json['orderDate'])
          : DateTime.now(),
      orderUpdateDate: json['orderUpdateDate'] != null
          ? DateTime.parse(json['orderUpdateDate'])
          : null,
      paymentId: json['paymentId'],
      payerId: json['payerId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'cartId': cartId,
      'cartItems': cartItems.map((item) => item.toJson()).toList(),
      'addressInfo': addressInfo.toJson(),
      'assignedTo': assignedTo,
      'orderStatus': orderStatus,
      'paymentMethod': paymentMethod,
      'paymentStatus': paymentStatus,
      'totalAmount': totalAmount,
      'orderDate': orderDate.toIso8601String(),
      'orderUpdateDate': orderUpdateDate?.toIso8601String(),
      'paymentId': paymentId,
      'payerId': payerId,
    };
  }

  // For creating a new order
  Map<String, dynamic> toCreateJson() {
    return {
      'userId': userId,
      'cartId': cartId,
      'cartItems': cartItems.map((item) => item.toJson()).toList(),
      'addressInfo': addressInfo.toJson(),
      'orderStatus': orderStatus,
      'paymentMethod': paymentMethod,
      'paymentStatus': paymentStatus,
      'totalAmount': totalAmount,
      'orderDate': orderDate.toIso8601String(),
      'orderUpdateDate': orderUpdateDate?.toIso8601String(),
    };
  }

  bool get isDelivered => orderStatus == 'delivered';
  bool get isCancelled => orderStatus == 'cancelled';
  bool get isPending => orderStatus == 'pending';
  bool get isConfirmed => orderStatus == 'confirmed';
  bool get isProcessing => orderStatus == 'processing';
  bool get isShipped => orderStatus == 'shipped';


}
