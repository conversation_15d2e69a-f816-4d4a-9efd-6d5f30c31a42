import 'package:flutter/foundation.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

class SocketService {
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;

  late io.Socket _socket;
  bool _isConnected = false;

  // Callbacks
  Function(dynamic)? onNewOrder;
  Function(String)? onOrderAccepted;
  Function(String)? onOrderRejected;
  Function(dynamic)? onNewPrescription;
  Function(String, String)? onPrescriptionStatusUpdated;
  Function(String, String)? onPrescriptionAccepted;

  SocketService._internal() {
    _initSocket();
  }

  void _initSocket() {
    try {
      debugPrint('🔌 Initializing socket connection to ${AppConstants.socketUrl}');
      _socket = io.io(AppConstants.socketUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': true, // Auto-connect when initialized
        'reconnection': true, // Enable reconnection
        'reconnectionDelay': 1000, // Start with 1 second delay
        'reconnectionDelayMax': 5000, // Maximum 5 seconds delay
        'reconnectionAttempts': 5, // Try to reconnect 5 times
      });

      // Socket events
      _socket.on('connect', (_) {
        debugPrint('✅ Socket connected: ${_socket.id}');
        _isConnected = true;
      });

      _socket.on('disconnect', (_) {
        debugPrint('❌ Socket disconnected');
        _isConnected = false;
      });

      _socket.on('admin_new_order', (data) {
        debugPrint('📦 New order received: $data');
        if (onNewOrder != null) {
          onNewOrder!(data);
        }
      });

      _socket.on('order_accepted', (orderId) {
        debugPrint('✅ Order accepted: $orderId');
        if (onOrderAccepted != null) {
          onOrderAccepted!(orderId);
        }
      });

      _socket.on('order_rejected', (orderId) {
        debugPrint('❌ Order rejected: $orderId');
        if (onOrderRejected != null) {
          onOrderRejected!(orderId);
        }
      });

      _socket.on('admin_new_prescription', (data) {
        debugPrint('🔬 New prescription received: $data');
        if (onNewPrescription != null) {
          onNewPrescription!(data);
        }
      });

      _socket.on('prescription_status_updated', (data) {
        debugPrint('🔬 Prescription status updated: $data');
        if (onPrescriptionStatusUpdated != null && data is Map) {
          onPrescriptionStatusUpdated!(data['prescriptionId'], data['status']);
        }
      });

      _socket.on('prescription_accepted', (data) {
        debugPrint('✅ Prescription accepted: $data');
        if (onPrescriptionAccepted != null && data is Map) {
          onPrescriptionAccepted!(data['prescriptionId'], data['adminId']);
        }
      });

      _socket.on('connect_error', (error) {
        debugPrint('❌ Socket connection error: $error');
      });
    } catch (e) {
      debugPrint('❌ Socket initialization error: $e');
    }
  }

  void connect() {
    if (!_isConnected) {
      _socket.connect();
    }
  }

  void disconnect() {
    if (_isConnected) {
      _socket.disconnect();
    }
  }

  void emitNewOrder(dynamic orderData) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('📦 Emitting new order event: ${orderData['_id']}');
        _socket.emit('neworder', orderData);
      });
    } else {
      debugPrint('📦 Emitting new order event: ${orderData['_id']}');
      _socket.emit('neworder', orderData);
    }
  }

  void emitOrderAccepted(String orderId) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('✅ Emitting order accepted event: $orderId');
        _socket.emit('order_accepted', orderId);
      });
    } else {
      debugPrint('✅ Emitting order accepted event: $orderId');
      _socket.emit('order_accepted', orderId);
    }
  }

  void emitOrderRejected(String orderId) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('❌ Emitting order rejected event: $orderId');
        _socket.emit('order_rejected', orderId);
      });
    } else {
      debugPrint('❌ Emitting order rejected event: $orderId');
      _socket.emit('order_rejected', orderId);
    }
  }

  void emitNewPrescription(dynamic prescriptionData) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('📋 Emitting new prescription event: ${prescriptionData['prescriptionId']}');
        _socket.emit('new_prescription', prescriptionData);
      });
    } else {
      debugPrint('📋 Emitting new prescription event: ${prescriptionData['prescriptionId']}');
      _socket.emit('new_prescription', prescriptionData);
    }
  }

  void emitPrescriptionStatusUpdated(String prescriptionId, String status) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('🔄 Emitting prescription status updated event: $prescriptionId - $status');
        _socket.emit('prescription_status_updated', {
          'prescriptionId': prescriptionId,
          'status': status,
        });
      });
    } else {
      debugPrint('🔄 Emitting prescription status updated event: $prescriptionId - $status');
      _socket.emit('prescription_status_updated', {
        'prescriptionId': prescriptionId,
        'status': status,
      });
    }
  }

  void emitPrescriptionAccepted(String prescriptionId, String adminId) {
    // Make sure we're connected before sending the event
    if (!_isConnected) {
      debugPrint('⚠️ Socket not connected, attempting to connect...');
      connect();
      // Add a small delay to allow connection to establish
      Future.delayed(const Duration(milliseconds: 500), () {
        debugPrint('✅ Emitting prescription accepted event: $prescriptionId by $adminId');
        _socket.emit('prescription_accepted', {
          'prescriptionId': prescriptionId,
          'adminId': adminId,
        });
      });
    } else {
      debugPrint('✅ Emitting prescription accepted event: $prescriptionId by $adminId');
      _socket.emit('prescription_accepted', {
        'prescriptionId': prescriptionId,
        'adminId': adminId,
      });
    }
  }

  bool get isConnected => _isConnected;
}
