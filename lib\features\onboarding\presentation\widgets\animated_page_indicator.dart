import 'package:flutter/material.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class AnimatedPageIndicator extends StatefulWidget {
  final int count;
  final int currentPage;
  final PageController pageController;

  const AnimatedPageIndicator({
    Key? key,
    required this.count,
    required this.currentPage,
    required this.pageController,
  }) : super(key: key);

  @override
  State<AnimatedPageIndicator> createState() => _AnimatedPageIndicatorState();
}

class _AnimatedPageIndicatorState extends State<AnimatedPageIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _progressAnimation = Tween<double>(
      begin: widget.currentPage / (widget.count - 1),
      end: widget.currentPage / (widget.count - 1),
    ).animate(_animationController);

    widget.pageController.addListener(_updateProgressAnimation);
  }

  @override
  void didUpdateWidget(AnimatedPageIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.currentPage != widget.currentPage) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.currentPage / (widget.count - 1),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));

      _animationController.forward(from: 0.0);
    }
  }

  void _updateProgressAnimation() {
    if (!widget.pageController.hasClients) return;

    final double page = widget.pageController.page ?? 0;
    final progress = page / (widget.count - 1);

    setState(() {
      _progressAnimation = Tween<double>(
        begin: progress,
        end: progress,
      ).animate(_animationController);
    });
  }

  @override
  void dispose() {
    widget.pageController.removeListener(_updateProgressAnimation);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(180),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: ScreenConstants.primaryGradientStart.withAlpha(10),
                blurRadius: 8,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(widget.count, (index) {
              final isActive = index == widget.currentPage;

              return GestureDetector(
                onTap: () {
                  widget.pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                  );
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  width: isActive ? 25 : 10,
                  height: 10,
                  decoration: BoxDecoration(
                    gradient: isActive
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              ScreenConstants.primaryGradientStart,
                              ScreenConstants.primaryGradientEnd,
                            ],
                          )
                        : null,
                    color: isActive ? null : Colors.grey.withAlpha(50), // Lighter inactive dots
                    borderRadius: BorderRadius.circular(5),
                    boxShadow: isActive ? [
                      BoxShadow(
                        color: ScreenConstants.primaryGradientStart.withAlpha(76), // 0.3 opacity
                        blurRadius: 4,
                        spreadRadius: 0,
                        offset: const Offset(0, 1),
                      ),
                    ] : null,
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
