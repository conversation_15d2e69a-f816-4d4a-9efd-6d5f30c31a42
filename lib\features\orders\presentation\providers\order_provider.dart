import 'package:flutter/foundation.dart';
import 'package:medicine_shop/core/network/socket_service.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';
import 'package:medicine_shop/features/cart/domain/models/cart_model.dart';
import 'package:medicine_shop/features/orders/data/order_service.dart';
import 'package:medicine_shop/features/orders/domain/models/order_model.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';

enum OrderStatus {
  initial,
  loading,
  loaded,
  error,
}

class OrderProvider with ChangeNotifier {
  final OrderService _orderService;
  final SocketService _socketService;

  OrderStatus _status = OrderStatus.initial;
  List<OrderModel> _orders = [];
  OrderModel? _selectedOrder;
  String? _errorMessage;
  String? _pendingOrderId;
  String? _razorpayOrderId;
  int? _razorpayAmount;
  bool _hasNewOrders = false;

  OrderProvider({
    OrderService? orderService,
    SocketService? socketService,
  }) :
    _orderService = orderService ?? OrderService(),
    _socketService = socketService ?? SocketService() {
    // Initialize socket listeners
    _initSocketListeners();
  }

  void _initSocketListeners() {
    _socketService.onNewOrder = (data) {
      debugPrint("📦 New order received via socket: $data");
      _hasNewOrders = true;
      // Refresh the unassigned orders list if we're on that screen
      if (_status == OrderStatus.loaded) {
        loadUnassignedOrders();
      }
      notifyListeners();
    };

    _socketService.onOrderAccepted = (orderId) {
      debugPrint("✅ Order accepted via socket: $orderId");
      // Update order status if it's in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        // Refresh the order details and lists
        loadOrderDetails(orderId);
        // Refresh the appropriate list based on current view
        if (_status == OrderStatus.loaded) {
          loadUnassignedOrders();
        }
      }
    };

    _socketService.onOrderRejected = (orderId) {
      debugPrint("❌ Order rejected via socket: $orderId");
      // Update order status if it's in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        // Refresh the order details and lists
        loadOrderDetails(orderId);
        // Refresh the appropriate list based on current view
        if (_status == OrderStatus.loaded) {
          loadUnassignedOrders();
        }
      }
    };

    // Connect to socket server
    _socketService.connect();
    debugPrint("🔌 Socket connection initialized");
  }

  // Getters
  OrderStatus get status => _status;
  List<OrderModel> get orders => _orders;
  OrderModel? get selectedOrder => _selectedOrder;
  String? get errorMessage => _errorMessage;
  String? get pendingOrderId => _pendingOrderId;
  String? get razorpayOrderId => _razorpayOrderId;
  int? get razorpayAmount => _razorpayAmount;
  bool get hasOrders => _orders.isNotEmpty;
  bool get hasNewOrders => _hasNewOrders;

  // Clear new orders flag
  void clearNewOrdersFlag() {
    _hasNewOrders = false;
    notifyListeners();
  }

  // Load user orders
  Future<void> loadUserOrders(UserModel user) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final orders = await _orderService.getUserOrders(user.id);

      // Sort orders by date in descending order (newest first)
      orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));

      _orders = orders;
      _status = OrderStatus.loaded;
      debugPrint("✅ Loaded ${orders.length} user orders in provider");
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      debugPrint("❌ Error loading user orders: $_errorMessage");
    }

    notifyListeners();
  }

  // Load order details
  Future<void> loadOrderDetails(String orderId) async {
    // If the order is already loaded and it's the same order, don't reload
    if (_selectedOrder != null && _selectedOrder!.id == orderId && _status == OrderStatus.loaded) {
      // Just return without making an API call
      return;
    }

    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final order = await _orderService.getOrderDetails(orderId);
      _selectedOrder = order;
      _status = OrderStatus.loaded;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Create a new order
  Future<bool> createOrder({
    required UserModel user,
    required CartModel cart,
    required AddressModel address,
    required String paymentMethod,
  }) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    _pendingOrderId = null;
    notifyListeners();

    try {
      final orderModel = OrderModel(
        id: '', // Will be assigned by the server
        userId: user.id,
        cartId: cart.id,
        cartItems: cart.items,
        addressInfo: OrderAddressInfo.fromAddressModel(address),
        orderStatus: 'pending',
        paymentMethod: paymentMethod,
        paymentStatus: 'pending',
        totalAmount: cart.subtotal,
        orderDate: DateTime.now(),
      );

      final result = await _orderService.createOrder(orderModel);

      _pendingOrderId = result['orderId'];

      // For Razorpay, store the order details
      if (paymentMethod == 'razorpay') {
        _razorpayOrderId = result['razorpayOrderId'];
        _razorpayAmount = result['amount'];
      }

      // Emit socket event for new order with complete order data
      // This ensures the admin panel receives all the necessary information
      _socketService.emitNewOrder({
        '_id': _pendingOrderId, // Use _id to match the format expected by the admin panel
        'orderId': _pendingOrderId,
        'userId': user.id,
        'userName': user.userName, // Include user name for display in admin panel
        'totalAmount': cart.subtotal,
        'items': cart.totalItems,
        'cartItems': cart.items.map((item) => item.toJson()).toList(), // Include cart items
        'addressInfo': address.toJson(), // Include address info
        'paymentMethod': paymentMethod,
        'orderStatus': 'pending',
        'orderDate': DateTime.now().toIso8601String(), // Format date as ISO string
      });

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Capture payment (legacy method, kept for backward compatibility)
  Future<bool> capturePayment(String orderId, String paymentId, String payerId, {String? signature}) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final order = await _orderService.capturePayment(orderId, paymentId, payerId, signature: signature);

      // Add the new order to the list
      _orders.add(order);
      _selectedOrder = order;
      _pendingOrderId = null;

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Capture Razorpay payment with named parameters for clarity
  Future<bool> captureRazorpayPayment({
    required String orderId,
    required String razorpayPaymentId,
    required String razorpayOrderId,
    required String razorpaySignature,
  }) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      debugPrint('📦 Capturing Razorpay payment for order: $orderId');
      debugPrint('Payment ID: $razorpayPaymentId');
      debugPrint('Order ID: $razorpayOrderId');
      debugPrint('Signature: $razorpaySignature');

      // Use the new captureRazorpayPayment method in the service
      final order = await _orderService.captureRazorpayPayment(
        orderId: orderId,
        razorpayPaymentId: razorpayPaymentId,
        razorpayOrderId: razorpayOrderId,
        razorpaySignature: razorpaySignature,
      );

      // Add the new order to the list
      _orders.add(order);
      _selectedOrder = order;
      _pendingOrderId = null;
      _razorpayOrderId = null;
      _razorpayAmount = null;

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('❌ Error capturing Razorpay payment: $e');
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Admin: Load all orders
  Future<void> loadAllOrders() async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final orders = await _orderService.getAllOrders();
      _orders = orders;
      _status = OrderStatus.loaded;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Admin: Update order status
  Future<bool> updateOrderStatus(String orderId, String status) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedOrder = await _orderService.updateOrderStatus(orderId, status);

      // Update the order in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        _orders[index] = updatedOrder;
      }

      // If this was the selected order, update it
      if (_selectedOrder?.id == orderId) {
        _selectedOrder = updatedOrder;
      }

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Admin: Load unassigned orders
  Future<void> loadUnassignedOrders() async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final orders = await _orderService.getUnassignedOrders();
      _orders = orders;
      _hasNewOrders = false; // Reset new orders flag
      _status = OrderStatus.loaded;
      debugPrint("✅ Loaded ${orders.length} unassigned orders in provider");
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      debugPrint("❌ Error loading unassigned orders: $_errorMessage");
    }

    notifyListeners();
  }

  // Admin: Accept order
  Future<bool> acceptOrder(String orderId) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedOrder = await _orderService.acceptOrder(orderId);

      // Update the order in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        _orders[index] = updatedOrder;
      } else {
        // If not in the list, add it
        _orders.add(updatedOrder);
      }

      // If this was the selected order, update it
      if (_selectedOrder?.id == orderId) {
        _selectedOrder = updatedOrder;
      }

      // Emit socket event for order accepted
      _socketService.emitOrderAccepted(orderId);

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Admin: Load accepted orders
  Future<void> loadAcceptedOrders() async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final orders = await _orderService.getAcceptedOrders();

      // Sort orders by date in descending order (newest first)
      orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));

      _orders = orders;
      _status = OrderStatus.loaded;
      debugPrint("✅ Loaded ${orders.length} accepted orders in provider");
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      debugPrint("❌ Error loading accepted orders: $_errorMessage");
    }

    notifyListeners();
  }

  // Admin: Mark order as delivered
  Future<bool> markOrderAsDelivered(String orderId) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedOrder = await _orderService.markOrderAsDelivered(orderId);

      // Update the order in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        _orders[index] = updatedOrder;
      }

      // If this was the selected order, update it
      if (_selectedOrder?.id == orderId) {
        _selectedOrder = updatedOrder;
      }

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Admin: Reject order
  Future<bool> rejectOrder(String orderId) async {
    _status = OrderStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedOrder = await _orderService.rejectOrder(orderId);

      // Update the order in the list
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index >= 0) {
        _orders[index] = updatedOrder;
      } else {
        // If not in the list, add it
        _orders.add(updatedOrder);
      }

      // If this was the selected order, update it
      if (_selectedOrder?.id == orderId) {
        _selectedOrder = updatedOrder;
      }

      // Emit socket event for order rejected
      _socketService.emitOrderRejected(orderId);

      _status = OrderStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = OrderStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
