import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:medicine_shop/core/network/socket_service.dart';
import 'package:medicine_shop/features/admin/data/prescription_service.dart';
import 'package:medicine_shop/features/admin/domain/models/prescription_model.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';

enum PrescriptionStatus {
  initial,
  loading,
  loaded,
  error,
}

class PrescriptionProvider with ChangeNotifier {
  final PrescriptionService _prescriptionService;
  final SocketService _socketService;

  PrescriptionStatus _status = PrescriptionStatus.initial;
  List<PrescriptionModel> _prescriptions = [];
  PrescriptionModel? _selectedPrescription;
  String? _errorMessage;
  bool _hasNewPrescriptions = false;

  PrescriptionProvider({
    PrescriptionService? prescriptionService,
    SocketService? socketService,
  }) :
    _prescriptionService = prescriptionService ?? PrescriptionService(),
    _socketService = socketService ?? SocketService() {
    // Initialize socket listeners
    _initSocketListeners();
  }

  void _initSocketListeners() {
    _socketService.onNewPrescription = (data) {
      _hasNewPrescriptions = true;
      // Reload prescriptions when a new one is received
      loadAllPrescriptions();
      notifyListeners();
    };

    _socketService.onPrescriptionStatusUpdated = (prescriptionId, status) {
      // Update the prescription in the list if it exists
      final index = _prescriptions.indexWhere((p) => p.id == prescriptionId);
      if (index >= 0) {
        _prescriptions[index] = _prescriptions[index].copyWith(status: status);

        // If this was the selected prescription, update it
        if (_selectedPrescription?.id == prescriptionId) {
          _selectedPrescription = _prescriptions[index];
        }

        notifyListeners();
      }
    };

    // Connect to socket server
    _socketService.connect();
  }

  // Getters
  PrescriptionStatus get status => _status;
  List<PrescriptionModel> get prescriptions => _prescriptions;
  PrescriptionModel? get selectedPrescription => _selectedPrescription;
  String? get errorMessage => _errorMessage;
  bool get hasNewPrescriptions => _hasNewPrescriptions;
  bool get hasPrescriptions => _prescriptions.isNotEmpty;

  // Clear new prescriptions flag
  void clearNewPrescriptionsFlag() {
    _hasNewPrescriptions = false;
    notifyListeners();
  }

  // Load all prescriptions (for admin)
  Future<void> loadAllPrescriptions() async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final prescriptions = await _prescriptionService.getAllPrescriptions();
      _prescriptions = prescriptions;
      _status = PrescriptionStatus.loaded;
      _hasNewPrescriptions = false;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Load user prescriptions
  Future<void> loadUserPrescriptions(UserModel user) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final prescriptions = await _prescriptionService.getUserPrescriptions(user.id);
      _prescriptions = prescriptions;
      _status = PrescriptionStatus.loaded;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Upload prescription image and create prescription
  Future<bool> uploadPrescription(File imageFile, UserModel user, String adminId, String notes) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      // First upload the image
      final imageUrl = await _prescriptionService.uploadPrescriptionImage(imageFile);

      // Then create the prescription
      final newPrescription = PrescriptionModel(
        id: '', // Will be assigned by the server
        userId: user.id,
        adminId: adminId,
        imageUrl: imageUrl,
        notes: notes,
        status: 'pending',
        userName: user.userName, // Include user name
        createdAt: DateTime.now(),
      );

      final prescription = await _prescriptionService.addPrescription(newPrescription);

      // Add to the list
      _prescriptions.add(prescription);

      // Emit socket event for new prescription
      _socketService.emitNewPrescription({
        'prescriptionId': prescription.id,
        'userId': user.id,
        'adminId': adminId,
      });

      _status = PrescriptionStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update prescription status
  Future<bool> updatePrescriptionStatus(String prescriptionId, String status, [String? adminId]) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      PrescriptionModel updatedPrescription;

      // If adminId is provided, use acceptPrescription method
      if (adminId != null && adminId.isNotEmpty) {
        updatedPrescription = await _prescriptionService.acceptPrescription(
          prescriptionId,
          adminId,
        );

        // Also update the status if needed
        if (updatedPrescription.status != status) {
          updatedPrescription = await _prescriptionService.updatePrescriptionStatus(
            prescriptionId,
            status,
          );
        }
      } else {
        // Just update the status
        updatedPrescription = await _prescriptionService.updatePrescriptionStatus(
          prescriptionId,
          status,
        );
      }

      // Update in the list
      final index = _prescriptions.indexWhere((p) => p.id == prescriptionId);
      if (index >= 0) {
        _prescriptions[index] = updatedPrescription;
      }

      // If this was the selected prescription, update it
      if (_selectedPrescription?.id == prescriptionId) {
        _selectedPrescription = updatedPrescription;
      }

      // Emit socket event for prescription status update
      _socketService.emitPrescriptionStatusUpdated(prescriptionId, status);

      _status = PrescriptionStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Accept prescription and assign to admin
  Future<bool> acceptPrescription(String prescriptionId, String adminId) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedPrescription = await _prescriptionService.acceptPrescription(
        prescriptionId,
        adminId,
      );

      // Update in the list
      final index = _prescriptions.indexWhere((p) => p.id == prescriptionId);
      if (index >= 0) {
        _prescriptions[index] = updatedPrescription;
      }

      // If this was the selected prescription, update it
      if (_selectedPrescription?.id == prescriptionId) {
        _selectedPrescription = updatedPrescription;
      }

      // Emit socket event for prescription accepted
      _socketService.emitPrescriptionAccepted(prescriptionId, adminId);

      _status = PrescriptionStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Delete prescription
  Future<bool> deletePrescription(String prescriptionId) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _prescriptionService.deletePrescription(prescriptionId);

      if (success) {
        // Remove from the list
        _prescriptions.removeWhere((p) => p.id == prescriptionId);

        // If this was the selected prescription, clear it
        if (_selectedPrescription?.id == prescriptionId) {
          _selectedPrescription = null;
        }
      }

      _status = PrescriptionStatus.loaded;
      notifyListeners();
      return success;
    } catch (e) {
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Select a prescription
  void selectPrescription(String prescriptionId) {
    final index = _prescriptions.indexWhere((p) => p.id == prescriptionId);
    if (index >= 0) {
      _selectedPrescription = _prescriptions[index];
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Upload prescription image using direct upload endpoint and create prescription
  Future<bool> uploadPrescriptionDirect(File imageFile, UserModel user, String adminId, String notes, {
    required String addressId,
    required String address,
    required String city,
    required String pincode,
    required String phone,
  }) async {
    _status = PrescriptionStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      // Validate required address fields
      if (addressId.isEmpty || address.isEmpty || city.isEmpty || pincode.isEmpty || phone.isEmpty) {
        throw Exception('Please select a valid delivery address');
      }

      // Validate user information
      if (user.id.isEmpty || user.userName.isEmpty) {
        throw Exception('User information is incomplete');
      }

      print('Uploading prescription for user: ${user.id}, name: ${user.userName}');

      // Upload the image using the direct upload endpoint with all required fields
      final imageUrl = await _prescriptionService.uploadPrescriptionImageDirect(
        imageFile,
        userId: user.id,
        userName: user.userName,
        addressId: addressId,
        address: address,
        city: city,
        pincode: pincode,
        phone: phone,
        notes: notes,
      );

      // Create a new prescription model to add to the local list
      final newPrescription = PrescriptionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(), // Temporary ID until we reload
        userId: user.id,
        adminId: adminId,
        imageUrl: imageUrl,
        notes: notes,
        status: 'pending',
        userName: user.userName,
        createdAt: DateTime.now(),
      );

      // Add to the local list temporarily
      _prescriptions.add(newPrescription);
      notifyListeners();

      // Reload prescriptions to get the newly created one with the correct ID from the server
      await loadUserPrescriptions(user);

      // Update status
      _status = PrescriptionStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      print('Error uploading prescription: $e');
      _status = PrescriptionStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }
}
