import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/custom_error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/admin/presentation/widgets/prescription_status_badge.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';

class PrescriptionDetailsScreen extends StatefulWidget {
  final String prescriptionId;

  const PrescriptionDetailsScreen({
    super.key,
    required this.prescriptionId,
  });

  @override
  State<PrescriptionDetailsScreen> createState() => _PrescriptionDetailsScreenState();
}

class _PrescriptionDetailsScreenState extends State<PrescriptionDetailsScreen> {
  void _showStatusUpdateDialog(BuildContext context) {
    final prescription = Provider.of<PrescriptionProvider>(context, listen: false).selectedPrescription;
    if (prescription == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Prescription Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current status: ${prescription.status}'),
            const SizedBox(height: 16),
            const Text('Select new status:'),
            const SizedBox(height: 8),
            _buildStatusButton(context, prescription.status, 'pending'),
            _buildStatusButton(context, prescription.status, 'processing'),
            _buildStatusButton(context, prescription.status, 'completed'),
            _buildStatusButton(context, prescription.status, 'rejected'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusButton(BuildContext context, String currentStatus, String status) {
    final isCurrentStatus = currentStatus == status;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: CustomButton(
        text: status.toUpperCase(),
        onPressed: isCurrentStatus
            ? () {}
            : () {
                Navigator.pop(context);
                _updatePrescriptionStatus(status);
              },
        type: isCurrentStatus ? ButtonType.disabled : _getButtonTypeForStatus(status),
        isFullWidth: true,
      ),
    );
  }

  ButtonType _getButtonTypeForStatus(String status) {
    switch (status) {
      case 'pending':
        return ButtonType.warning;
      case 'processing':
        return ButtonType.info;
      case 'completed':
        return ButtonType.success;
      case 'rejected':
        return ButtonType.danger;
      default:
        return ButtonType.primary;
    }
  }

  Future<void> _updatePrescriptionStatus(String status) async {
    final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

    try {
      final success = await prescriptionProvider.updatePrescriptionStatus(widget.prescriptionId, status);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Prescription status updated to $status'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _acceptPrescription() async {
    final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You must be logged in to accept prescriptions'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    try {
      final success = await prescriptionProvider.acceptPrescription(
        widget.prescriptionId,
        authProvider.user!.id,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Prescription accepted successfully'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );

        // Navigate back to the prescriptions list
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to accept prescription: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Prescription Details',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshing prescription details...'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<PrescriptionProvider>(
        builder: (context, prescriptionProvider, child) {
          if (prescriptionProvider.status == PrescriptionStatus.loading) {
            return const LoadingIndicator(message: 'Loading prescription details...');
          }

          if (prescriptionProvider.status == PrescriptionStatus.error) {
            return CustomErrorWidget(
              message: prescriptionProvider.errorMessage ?? 'Failed to load prescription details',
              onRetry: () => prescriptionProvider.loadAllPrescriptions(),
            );
          }

          final prescription = prescriptionProvider.selectedPrescription;

          if (prescription == null) {
            return const Center(
              child: Text('Prescription not found'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Prescription ID and Status
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Prescription #${prescription.id.substring(0, 8)}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            PrescriptionStatusBadge(status: prescription.status),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Date: ${DateFormat('MMMM d, yyyy - h:mm a').format(prescription.createdAt)}',
                          style: const TextStyle(
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Prescription Image
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Prescription Image',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GestureDetector(
                          onTap: () {
                            // Show full-screen image
                            showDialog(
                              context: context,
                              builder: (context) => Dialog(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    AppBar(
                                      title: const Text('Prescription Image'),
                                      automaticallyImplyLeading: false,
                                      actions: [
                                        IconButton(
                                          icon: const Icon(Icons.close),
                                          onPressed: () => Navigator.pop(context),
                                        ),
                                      ],
                                    ),
                                    InteractiveViewer(
                                      panEnabled: true,
                                      boundaryMargin: const EdgeInsets.all(20),
                                      minScale: 0.5,
                                      maxScale: 4,
                                      child: Image.network(
                                        _getFullImageUrl(prescription.imageUrl),
                                        fit: BoxFit.contain,
                                        loadingBuilder: (context, child, loadingProgress) {
                                          if (loadingProgress == null) return child;
                                          return Center(
                                            child: CircularProgressIndicator(
                                              value: loadingProgress.expectedTotalBytes != null
                                                  ? loadingProgress.cumulativeBytesLoaded /
                                                      loadingProgress.expectedTotalBytes!
                                                  : null,
                                            ),
                                          );
                                        },
                                        errorBuilder: (context, error, stackTrace) {
                                          print('Error loading image: $error');
                                          return Container(
                                            height: 300,
                                            color: Colors.grey[200],
                                            child: const Center(
                                              child: Icon(Icons.error, size: 50, color: Colors.grey),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          child: Container(
                            height: 300,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                _getFullImageUrl(prescription.imageUrl),
                                fit: BoxFit.contain,
                                loadingBuilder: (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Center(
                                    child: CircularProgressIndicator(
                                      value: loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress.cumulativeBytesLoaded /
                                              loadingProgress.expectedTotalBytes!
                                          : null,
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  print('Error loading image: $error');
                                  return Container(
                                    height: 300,
                                    color: Colors.grey[200],
                                    child: const Center(
                                      child: Icon(Icons.error, size: 50, color: Colors.grey),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Customer Information
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Customer Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            const Icon(Icons.person, color: Colors.grey),
                            const SizedBox(width: 8),
                            Text('Customer: ${prescription.userName}'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(Icons.badge, color: Colors.grey),
                            const SizedBox(width: 8),
                            Text('Customer ID: ${prescription.userId}'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Notes - Always show this section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Customer Notes',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        prescription.notes.isNotEmpty
                            ? Text(
                                prescription.notes,
                                style: const TextStyle(fontSize: 16),
                              )
                            : const Text(
                                'No notes provided by customer',
                                style: TextStyle(
                                  fontStyle: FontStyle.italic,
                                  color: Colors.grey,
                                ),
                              ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: CustomButton(
                        text: 'Update Status',
                        onPressed: () => _showStatusUpdateDialog(context),
                        type: ButtonType.primary,
                        icon: Icons.update,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: prescription.status == 'pending'
                        ? CustomButton(
                            text: 'Accept Prescription',
                            onPressed: () { _acceptPrescription(); },
                            type: ButtonType.success,
                            icon: Icons.check_circle,
                          )
                        : CustomButton(
                            text: 'Accept Prescription',
                            onPressed: () {},
                            type: ButtonType.disabled,
                            icon: Icons.check_circle,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Helper method to get full image URL
  String _getFullImageUrl(String imageUrl) {
    // If the URL is empty or null, return a placeholder
    if (imageUrl.isEmpty) {
      return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If it's already a Cloudinary URL, use it directly
    if (imageUrl.contains('cloudinary.com')) {
      // For Cloudinary raw URLs, convert them to image URLs if needed
      if (imageUrl.contains('/raw/upload/')) {
        return imageUrl.replaceAll('/raw/upload/', '/image/upload/');
      }
      return imageUrl;
    }

    // If it's any other full URL, use it directly
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Handle relative paths
    final baseUrl = AppConstants.baseUrl.replaceAll('/api', '');

    if (imageUrl.startsWith('/')) {
      // Path with leading slash
      return '$baseUrl$imageUrl';
    } else {
      // Path without leading slash
      return '$baseUrl/$imageUrl';
    }
  }
}
