import 'package:flutter/foundation.dart';

class NumberUtils {
  // Helper method to safely parse double values
  static double parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      if (value.isEmpty) return 0.0;
      try {
        // Remove any non-numeric characters except decimal point
        final cleanedValue = value.replaceAll(RegExp(r'[^\d.]'), '');
        return double.parse(cleanedValue);
      } catch (e) {
        debugPrint('Error parsing double from string: $value, error: $e');
        return 0.0;
      }
    }
    debugPrint('Unknown type for value: ${value.runtimeType}, value: $value');
    return 0.0;
  }

  // Helper method to safely parse int values
  static int parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      if (value.isEmpty) return 0;
      try {
        // Remove any non-numeric characters
        final cleanedValue = value.replaceAll(RegExp(r'[^\d]'), '');
        return int.parse(cleanedValue);
      } catch (e) {
        debugPrint('Error parsing int from string: $value, error: $e');
        return 0;
      }
    }
    debugPrint('Unknown type for value: ${value.runtimeType}, value: $value');
    return 0;
  }
}
