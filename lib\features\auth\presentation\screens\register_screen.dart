import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/auth/presentation/widgets/animated_auth_background.dart';
import 'package:medicine_shop/features/auth/presentation/widgets/animated_auth_button.dart';
import 'package:medicine_shop/features/auth/presentation/widgets/animated_auth_input.dart';
import 'package:medicine_shop/features/auth/presentation/widgets/animated_auth_logo.dart';

class RegisterScreen extends StatefulWidget {
  final VoidCallback onLoginPressed;

  const RegisterScreen({
    super.key,
    required this.onLoginPressed,
  });

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Focus nodes for animated inputs
  final _nameFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  final _roleFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isRoleFocused = false;
  String _selectedRole = 'user';

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _slideAnimation = Tween<double>(begin: 30, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuart,
      ),
    );

    // Start the animation
    _animationController.forward();

    // Add focus listener for role dropdown
    _roleFocusNode.addListener(() {
      setState(() {
        _isRoleFocused = _roleFocusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    // Dispose controllers
    _nameController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();

    // Dispose focus nodes
    _nameFocusNode.dispose();
    _phoneFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _roleFocusNode.dispose();

    // Dispose animation controller
    _animationController.dispose();

    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }

  Future<void> _register() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        final success = await authProvider.register(
          _nameController.text.trim(),
          _passwordController.text,
          phoneNumber: _phoneNumberController.text.trim(),
          email: _emailController.text.trim(),
          role: _selectedRole,
        );

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Registration successful! Please login.'),
                backgroundColor: AppTheme.primaryColor,
              ),
            );
            widget.onLoginPressed();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(authProvider.errorMessage ?? 'Registration failed'),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: AnimatedAuthBackground(
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeInAnimation,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: size.height * 0.05),

                      // Simple Animated Logo
                      AnimatedAuthLogo(
                        icon: Icons.medical_services,
                        title: 'Medicine Shop',
                        subtitle: 'Create a new account',
                      ),

                      SizedBox(height: size.height * 0.04),

                      // Name Field with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthInput(
                          label: 'Full Name',
                          hint: 'Enter your full name',
                          controller: _nameController,
                          prefixIcon: Icons.person_outline,
                          focusNode: _nameFocusNode,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => setState(() {}),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your name';
                            }
                            return null;
                          },
                          enabled: !_isLoading,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Phone Number Field with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthInput(
                          label: 'Phone Number (Optional)',
                          hint: 'Enter your phone number',
                          controller: _phoneNumberController,
                          keyboardType: TextInputType.phone,
                          prefixIcon: Icons.phone_outlined,
                          focusNode: _phoneFocusNode,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => setState(() {}),
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (!RegExp(r'^\d{10}$').hasMatch(value)) {
                                return 'Please enter a valid 10-digit phone number';
                              }
                            }
                            return null;
                          },
                          enabled: !_isLoading,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Email Field with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthInput(
                          label: 'Email',
                          hint: 'Enter your email address',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          prefixIcon: Icons.email_outlined,
                          focusNode: _emailFocusNode,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => setState(() {}),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                          enabled: !_isLoading,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Password Field with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthInput(
                          label: 'Password',
                          hint: 'Enter your password',
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          prefixIcon: Icons.lock_outline,
                          focusNode: _passwordFocusNode,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => setState(() {}),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                              color: _obscurePassword
                                  ? Colors.grey.withAlpha(150)
                                  : ScreenConstants.primaryGradientStart,
                              size: 22,
                            ),
                            splashRadius: 20,
                            onPressed: _togglePasswordVisibility,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                          enabled: !_isLoading,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Confirm Password Field with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthInput(
                          label: 'Confirm Password',
                          hint: 'Confirm your password',
                          controller: _confirmPasswordController,
                          obscureText: _obscureConfirmPassword,
                          prefixIcon: Icons.lock_outline,
                          focusNode: _confirmPasswordFocusNode,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => setState(() {}),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscureConfirmPassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                              color: _obscureConfirmPassword
                                  ? Colors.grey.withAlpha(150)
                                  : ScreenConstants.primaryGradientStart,
                              size: 22,
                            ),
                            splashRadius: 20,
                            onPressed: _toggleConfirmPasswordVisibility,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please confirm your password';
                            }
                            if (value != _passwordController.text) {
                              return 'Passwords do not match';
                            }
                            return null;
                          },
                          enabled: !_isLoading,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Role Selection - Styled to match other inputs
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Label
                            Padding(
                              padding: const EdgeInsets.only(left: 4, bottom: 8),
                              child: Text(
                                'Role',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: ScreenConstants.descriptionColor,
                                ),
                              ),
                            ),
                            // Dropdown with matching style to AnimatedAuthInput
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.easeInOut,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: _isRoleFocused
                                        ? ScreenConstants.primaryGradientStart.withAlpha(10)
                                        : Colors.black.withAlpha(5),
                                    blurRadius: 3,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                                border: Border.all(
                                  color: _isRoleFocused
                                      ? ScreenConstants.primaryGradientStart
                                      : Colors.grey.withAlpha(80),
                                  width: _isRoleFocused ? 2.0 : 1.5,
                                ),
                              ),
                              child: DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  prefixIcon: Padding(
                                    padding: const EdgeInsets.only(left: 12, right: 8),
                                    child: Icon(
                                      Icons.admin_panel_settings_outlined,
                                      color: _isRoleFocused
                                          ? ScreenConstants.primaryGradientStart
                                          : Colors.grey.withAlpha(150),
                                      size: 22,
                                    ),
                                  ),
                                  prefixIconConstraints: const BoxConstraints(
                                    minWidth: 45,
                                    minHeight: 45,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                  isDense: true,
                                ),
                                value: _selectedRole,
                                items: const [
                                  DropdownMenuItem(
                                    value: 'user',
                                    child: Text(
                                      'User',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.black87,
                                      ),
                                    )
                                  ),
                                  DropdownMenuItem(
                                    value: 'admin',
                                    child: Text(
                                      'Admin',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.black87,
                                      ),
                                    )
                                  ),
                                ],
                                onChanged: _isLoading ? null : (value) {
                                  setState(() {
                                    _selectedRole = value!;
                                  });
                                },
                                dropdownColor: Colors.white,
                                icon: Icon(
                                  Icons.arrow_drop_down,
                                  color: ScreenConstants.primaryGradientStart,
                                  size: 30,
                                ),
                                iconEnabledColor: ScreenConstants.primaryGradientStart,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: ScreenConstants.titleColor,
                                  fontWeight: FontWeight.w500,
                                ),
                                focusNode: _roleFocusNode,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Register Button with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: AnimatedAuthButton(
                          text: 'Create Account',
                          onPressed: _register,
                          isLoading: _isLoading,
                          icon: Icons.app_registration,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Login Link with animation
                      Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Already have an account? ",
                              style: TextStyle(
                                fontSize: 15,
                                color: ScreenConstants.descriptionColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextButton(
                              onPressed: _isLoading ? null : widget.onLoginPressed,
                              style: TextButton.styleFrom(
                                foregroundColor: ScreenConstants.primaryGradientStart,
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              ),
                              child: const Text(
                                'Login',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: size.height * 0.04),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
