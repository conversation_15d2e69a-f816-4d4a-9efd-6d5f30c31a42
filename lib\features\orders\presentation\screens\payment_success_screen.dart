import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/features/cart/presentation/providers/cart_provider.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';

class PaymentSuccessScreen extends StatefulWidget {
  final String orderId;
  final String paymentId;
  final String payerId;

  const PaymentSuccessScreen({
    super.key,
    required this.orderId,
    required this.paymentId,
    required this.payerId,
  });

  @override
  State<PaymentSuccessScreen> createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends State<PaymentSuccessScreen> {
  bool _isProcessing = true;
  bool _isSuccess = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _capturePayment();
  }

  Future<void> _capturePayment() async {
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final cartProvider = Provider.of<CartProvider>(context, listen: false);

      final success = await orderProvider.capturePayment(
        widget.orderId,
        widget.paymentId,
        widget.payerId,
      );

      if (success && mounted) {
        setState(() {
          _isSuccess = true;
          _isProcessing = false;
        });

        // Clear cart
        cartProvider.clearCart();
      } else if (mounted) {
        setState(() {
          _isSuccess = false;
          _isProcessing = false;
          _errorMessage = orderProvider.errorMessage ?? 'Failed to complete payment';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSuccess = false;
          _isProcessing = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Status'),
        automaticallyImplyLeading: false,
      ),
      body: _isProcessing
          ? const LoadingIndicator(message: 'Processing your payment...')
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isSuccess ? Icons.check_circle : Icons.error,
              size: 80,
              color: _isSuccess ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 24),
            Text(
              _isSuccess ? 'Payment Successful!' : 'Payment Failed',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _isSuccess
                  ? 'Your order has been placed successfully. You can track your order in the Orders section.'
                  : _errorMessage ?? 'There was an error processing your payment. Please try again.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: _isSuccess ? Colors.black87 : Colors.red,
              ),
            ),
            const SizedBox(height: 32),
            if (_isSuccess) ...[
              CustomButton(
                text: 'View Order',
                onPressed: () {
                  Navigator.pushReplacementNamed(context, '/orders');
                },
                type: ButtonType.primary,
                icon: Icons.visibility,
              ),
              const SizedBox(height: 16),
              CustomButton(
                text: 'Continue Shopping',
                onPressed: () {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    '/products',
                    (route) => false,
                  );
                },
                type: ButtonType.secondary,
                icon: Icons.shopping_bag,
              ),
            ] else ...[
              CustomButton(
                text: 'Try Again',
                onPressed: () {
                  Navigator.pushReplacementNamed(context, '/checkout');
                },
                type: ButtonType.primary,
                icon: Icons.refresh,
              ),
              const SizedBox(height: 16),
              CustomButton(
                text: 'Back to Cart',
                onPressed: () {
                  Navigator.pushReplacementNamed(context, '/cart');
                },
                type: ButtonType.secondary,
                icon: Icons.shopping_cart,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
