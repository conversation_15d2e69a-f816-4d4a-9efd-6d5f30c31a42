import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/widgets/bottom_nav_app_bar.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/features/admin/domain/models/prescription_model.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';


class AdminPrescriptionsScreen extends StatefulWidget {
  const AdminPrescriptionsScreen({super.key});

  @override
  State<AdminPrescriptionsScreen> createState() => _AdminPrescriptionsScreenState();
}

class _AdminPrescriptionsScreenState extends State<AdminPrescriptionsScreen> {
  @override
  void initState() {
    super.initState();
    // Load prescriptions when the screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BottomNavAppBar(
        title: 'Customer Prescriptions',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Prescriptions refreshed'),
                  duration: const Duration(seconds: 1),
                  backgroundColor: ScreenConstants.primaryGradientStart,
                ),
              );
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Consumer<PrescriptionProvider>(
        builder: (context, prescriptionProvider, child) {
          if (prescriptionProvider.status == PrescriptionStatus.loading) {
            return const LoadingIndicator(message: 'Loading prescriptions...');
          }

          if (prescriptionProvider.status == PrescriptionStatus.error) {
            return CustomErrorWidget(
              message: prescriptionProvider.errorMessage ?? 'Failed to load prescriptions',
              onRetry: () => prescriptionProvider.loadAllPrescriptions(),
            );
          }

          final prescriptions = prescriptionProvider.prescriptions;

          if (prescriptions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.description_outlined,
                    size: 80,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No prescriptions found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Customer uploaded prescriptions will appear here',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 24),
                  CustomButton(
                    text: 'Refresh',
                    onPressed: () {
                      prescriptionProvider.loadAllPrescriptions();
                    },
                    type: ButtonType.primary,
                    icon: Icons.refresh,
                    isFullWidth: false,
                  ),
                ],
              ),
            );
          }

          // Separate prescriptions into assigned and unassigned
          final unassignedPrescriptions = prescriptions
              .where((p) => p.adminId.isEmpty && p.status.toLowerCase() == 'pending')
              .toList();

          final assignedPrescriptions = prescriptions
              .where((p) => p.adminId.isNotEmpty || p.status.toLowerCase() != 'pending')
              .toList();

          return DefaultTabController(
            length: 2,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        ScreenConstants.primaryGradientStart.withAlpha(30),
                        ScreenConstants.primaryGradientEnd.withAlpha(20),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: ScreenConstants.primaryGradientStart.withAlpha(20),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: const Color(0x33FFFFFF), // White with 20% opacity
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: TabBar(
                      tabs: [
                        Tab(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(Icons.pending_actions, size: 20),
                                SizedBox(width: 8),
                                Text('Unassigned', style: TextStyle(fontSize: 14)),
                              ],
                            ),
                          ),
                        ),
                        Tab(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(Icons.assignment_turned_in, size: 20),
                                SizedBox(width: 8),
                                Text('Assigned', style: TextStyle(fontSize: 14)),
                              ],
                            ),
                          ),
                        ),
                      ],
                      indicator: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            ScreenConstants.primaryGradientStart,
                            ScreenConstants.primaryGradientEnd,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: ScreenConstants.primaryGradientStart.withAlpha(40),
                            blurRadius: 8,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      labelColor: Colors.white,
                      unselectedLabelColor: ScreenConstants.descriptionColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                      unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
                      dividerColor: Colors.transparent,
                    ),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      // Unassigned Prescriptions Tab
                      unassignedPrescriptions.isEmpty
                          ? _buildEmptyState('No unassigned prescriptions', 'Waiting for new prescriptions')
                          : _buildPrescriptionGrid(unassignedPrescriptions, prescriptionProvider),

                      // Assigned Prescriptions Tab
                      assignedPrescriptions.isEmpty
                          ? _buildEmptyState('No assigned prescriptions', 'Assign prescriptions to see them here')
                          : _buildPrescriptionGrid(assignedPrescriptions, prescriptionProvider),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: Container(
        height: 60,
        width: 60,
        margin: const EdgeInsets.only(bottom: 80, right: 8), // Adjust for floating nav bar
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              ScreenConstants.primaryGradientStart,
              ScreenConstants.primaryGradientEnd,
            ],
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: ScreenConstants.primaryGradientStart.withAlpha(60),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: ScreenConstants.primaryGradientEnd.withAlpha(40),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(30),
            onTap: () {
              Navigator.pushNamed(context, '/admin/upload-prescription');
            },
            child: const Center(
              child: Icon(
                Icons.upload_file,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPrescriptionCard(BuildContext context, PrescriptionModel prescription, PrescriptionProvider prescriptionProvider) {
    final bool isUnassigned = prescription.adminId.isEmpty && prescription.status.toLowerCase() == 'pending';

    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 4,
      child: InkWell(
        onTap: () {
          // Navigate to prescription details
          prescriptionProvider.selectPrescription(prescription.id);
          Navigator.pushNamed(
            context,
            '/admin/prescription-details',
            arguments: prescription.id,
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Bar
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
              color: _getStatusColor(prescription.status),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    prescription.status.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isUnassigned)
                    GestureDetector(
                      onTap: () {
                        // Show dialog to assign prescription
                        _showAssignDialog(context, prescription, prescriptionProvider);
                      },
                      child: const Icon(
                        Icons.assignment_add,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                ],
              ),
            ),

            // Prescription Image
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    _getFullImageUrl(prescription.imageUrl),
                    fit: BoxFit.cover,
                    width: double.infinity,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      // Error handled silently
                      return Container(
                        color: Colors.grey[200],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error, size: 40, color: Colors.red),
                            const SizedBox(height: 8),
                            Text(
                              'Error loading image',
                              style: TextStyle(color: Colors.grey[600]),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    },
                  ),

                  // Overlay for date
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                      color: Colors.black.withAlpha(128),
                      child: Text(
                        DateFormat('MMM d, yyyy').format(prescription.createdAt),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Prescription Info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer Name
                  Text(
                    prescription.userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // Notes section - always show
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.notes,
                        size: 14,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      const Text(
                        'Notes:',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          prescription.notes.isNotEmpty
                              ? prescription.notes
                              : 'None',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: prescription.notes.isEmpty ? FontStyle.italic : FontStyle.normal,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to show assignment dialog
  void _showAssignDialog(BuildContext context, PrescriptionModel prescription, PrescriptionProvider prescriptionProvider) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assign Prescription'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Assign this prescription to yourself?'),
            const SizedBox(height: 8),
            Text(
              'Customer: ${prescription.userName}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text('Date: ${DateFormat('MMM d, yyyy').format(prescription.createdAt)}'),
            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 8),
            const Text(
              'Customer Notes:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                prescription.notes.isNotEmpty
                    ? prescription.notes
                    : 'No notes provided by customer',
                style: TextStyle(
                  fontStyle: prescription.notes.isEmpty ? FontStyle.italic : FontStyle.normal,
                  color: prescription.notes.isEmpty ? Colors.grey : Colors.black,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store the context for later use
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              final navigator = Navigator.of(context);

              // Close the dialog
              navigator.pop();

              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Assigning prescription...'),
                    ],
                  ),
                ),
              );

              try {
                // Update prescription status
                await prescriptionProvider.updatePrescriptionStatus(
                  prescription.id,
                  'processing',
                  authProvider.user!.id,
                );

                // Check if widget is still mounted before using context
                if (!mounted) return;

                // Close loading dialog
                navigator.pop();

                // Show success message
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Prescription assigned successfully'),
                    backgroundColor: Colors.green,
                  ),
                );

                // Refresh prescriptions
                prescriptionProvider.loadAllPrescriptions();
              } catch (e) {
                // Check if widget is still mounted before using context
                if (!mounted) return;

                // Close loading dialog
                navigator.pop();

                // Show error message
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('Failed to assign prescription: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Assign to Me'),
          ),
        ],
      ),
    );
  }



  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'processing':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  // Helper method to build an empty state widget
  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.description_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Refresh',
            onPressed: () {
              Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
            },
            type: ButtonType.primary,
            icon: Icons.refresh,
            isFullWidth: false,
          ),
        ],
      ),
    );
  }

  // Helper method to build a grid of prescriptions
  Widget _buildPrescriptionGrid(List<PrescriptionModel> prescriptions, PrescriptionProvider prescriptionProvider) {
    return GridView.builder(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 96), // Extra bottom padding for floating nav bar
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: prescriptions.length,
      itemBuilder: (context, index) {
        final prescription = prescriptions[index];
        return _buildPrescriptionCard(context, prescription, prescriptionProvider);
      },
    );
  }

  // Helper method to get full image URL
  String _getFullImageUrl(String imageUrl) {
    // If the URL is empty or null, return a placeholder
    if (imageUrl.isEmpty) {
      return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If it's already a Cloudinary URL, use it directly
    if (imageUrl.contains('cloudinary.com')) {
      // For Cloudinary raw URLs, convert them to image URLs if needed
      if (imageUrl.contains('/raw/upload/')) {
        return imageUrl.replaceAll('/raw/upload/', '/image/upload/');
      }
      return imageUrl;
    }

    // If it's any other full URL, use it directly
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Handle relative paths
    final baseUrl = AppConstants.baseUrl.replaceAll('/api', '');

    if (imageUrl.startsWith('/')) {
      // Path with leading slash
      return '$baseUrl$imageUrl';
    } else {
      // Path without leading slash
      return '$baseUrl/$imageUrl';
    }
  }
}
