import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/animated_bottom_navigation.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/auth/presentation/screens/auth_wrapper.dart';
import 'package:medicine_shop/features/cart/presentation/providers/cart_provider.dart';
import 'package:medicine_shop/features/cart/presentation/screens/cart_screen.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';
import 'package:medicine_shop/features/products/presentation/providers/product_provider.dart';
import 'package:medicine_shop/features/products/presentation/screens/products_screen.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';
import 'package:medicine_shop/features/profile/presentation/screens/profile_screen.dart';
import 'package:medicine_shop/features/orders/presentation/screens/checkout_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/admin_dashboard_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/product_form_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/order_details_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/prescription_upload_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/prescription_list_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/prescription_details_screen.dart';
import 'package:medicine_shop/features/profile/presentation/screens/prescription_screen.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/admin/presentation/providers/user_provider.dart';
import 'package:medicine_shop/features/orders/presentation/screens/orders_screen.dart';
import 'package:medicine_shop/features/orders/presentation/screens/payment_success_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/unassigned_orders_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/order_history_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/admin_orders_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/admin_prescriptions_screen.dart';
import 'package:medicine_shop/features/profile/presentation/screens/user_prescription_upload_screen.dart';
import 'package:medicine_shop/features/profile/presentation/screens/user_prescription_history_screen.dart';
import 'package:medicine_shop/features/splash/presentation/screens/splash_screen.dart';

Future<void> main() async {
  // Load environment variables
  await dotenv.load(fileName: '.env');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => AddressProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => PrescriptionProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
      ],
      child: MaterialApp(
        title: 'Medicine Shop',
        home: const AppWrapper(),
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        routes: {
          '/auth': (context) => const AuthWrapper(),
          '/home': (context) => const MainApp(),
          '/products': (context) => const ProductsScreen(),
          '/cart': (context) => const CartScreen(),
          '/profile': (context) => const ProfileScreen(),
          '/profile/add-address': (context) => const ProfileScreen(initialTab: 1),
          '/checkout': (context) => const CheckoutScreen(),
          '/orders': (context) => const OrdersScreen(),
          '/payment/success': (context) {
            final args = ModalRoute.of(context)!.settings.arguments as Map<String, String>;
            return PaymentSuccessScreen(
              orderId: args['orderId']!,
              paymentId: args['paymentId']!,
              payerId: args['payerId']!,
            );
          },
          '/admin': (context) => const AdminDashboardScreen(),
          '/admin/add-product': (context) => const ProductFormScreen(),
          '/admin/edit-product': (context) => ProductFormScreen(
            product: ModalRoute.of(context)!.settings.arguments as ProductModel,
            isEditing: true,
          ),
          '/admin/order-details': (context) => AdminOrderDetailsScreen(
            orderId: ModalRoute.of(context)!.settings.arguments as String,
          ),
          '/admin/upload-prescription': (context) => const PrescriptionUploadScreen(),
          '/admin/prescriptions': (context) => const PrescriptionListScreen(),
          '/admin/prescription-details': (context) => PrescriptionDetailsScreen(
            prescriptionId: ModalRoute.of(context)!.settings.arguments as String,
          ),
          '/admin/unassigned-orders': (context) => const UnassignedOrdersScreen(),
          '/admin/order-history': (context) => const OrderHistoryScreen(),
          '/admin/orders': (context) => const AdminOrdersScreen(),
          '/profile/prescriptions': (context) => const PrescriptionScreen(),
          '/profile/upload-prescription': (context) => const UserPrescriptionUploadScreen(),
          '/profile/prescription-history': (context) => const UserPrescriptionHistoryScreen(),
        },
      ),
    );
  }
}

class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  @override
  void initState() {
    super.initState();

    // Ensure auth provider is initialized immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // This forces the auth provider to check for existing login
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Also initialize address provider if user is authenticated
      if (authProvider.isAuthenticated && authProvider.user != null) {
        final addressProvider = Provider.of<AddressProvider>(context, listen: false);
        addressProvider.loadAddresses(authProvider.user!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Always show splash screen first
    return const SplashScreen();
  }
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  int _currentIndex = 0;

  List<Widget> _getScreens(bool isAdmin) {
    if (isAdmin) {
      // Admin users get different screens
      return [
        const AdminDashboardScreen(),
        const AdminOrdersScreen(),
        const AdminPrescriptionsScreen(),
      ];
    } else {
      // Regular users get the standard screens
      return [
        const ProductsScreen(),
        const CartScreen(),
        const PrescriptionScreen(),
        const ProfileScreen(),
      ];
    }
  }

  @override
  void initState() {
    super.initState();
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final cartProvider = Provider.of<CartProvider>(context, listen: false);
      final addressProvider = Provider.of<AddressProvider>(context, listen: false);

      if (authProvider.user != null) {
        // Load cart data
        cartProvider.loadCart(authProvider.user!);

        // Load address data
        addressProvider.loadAddresses(authProvider.user!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isAdmin = authProvider.isAdmin;
    final screens = _getScreens(isAdmin);

    // Ensure current index is valid
    if (_currentIndex >= screens.length) {
      _currentIndex = 0;
    }

    return Scaffold(
      body: screens[_currentIndex],
      bottomNavigationBar: AnimatedBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: isAdmin ? [
          // Admin navigation items
          const BottomNavigationBarItem(
            icon: Icon(Icons.admin_panel_settings_outlined),
            activeIcon: Icon(Icons.admin_panel_settings, size: 28),
            label: 'Admin',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag_outlined),
            activeIcon: Icon(Icons.shopping_bag, size: 28),
            label: 'Orders',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.description_outlined),
            activeIcon: Icon(Icons.description, size: 28),
            label: 'Prescriptions',
          ),
        ] : [
          // Regular user navigation items
          const BottomNavigationBarItem(
            icon: Icon(Icons.medical_services_outlined),
            activeIcon: Icon(Icons.medical_services, size: 28),
            label: 'Medicines',
          ),
          BottomNavigationBarItem(
            icon: Consumer<CartProvider>(
              builder: (context, cartProvider, child) {
                return Stack(
                  clipBehavior: Clip.none,
                  children: [
                    const Icon(Icons.shopping_cart_outlined),
                    if (cartProvider.itemCount > 0)
                      Positioned(
                        right: -8,
                        top: -8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: ScreenConstants.accentColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: ScreenConstants.accentColor.withAlpha(100),
                                blurRadius: 4,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 18,
                            minHeight: 18,
                          ),
                          child: Text(
                            '${cartProvider.itemCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
            activeIcon: Consumer<CartProvider>(
              builder: (context, cartProvider, child) {
                return Stack(
                  clipBehavior: Clip.none,
                  children: [
                    const Icon(Icons.shopping_cart, size: 28),
                    if (cartProvider.itemCount > 0)
                      Positioned(
                        right: -8,
                        top: -8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: ScreenConstants.accentColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: ScreenConstants.accentColor.withAlpha(100),
                                blurRadius: 4,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 18,
                            minHeight: 18,
                          ),
                          child: Text(
                            '${cartProvider.itemCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
            label: 'Cart',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.description_outlined),
            activeIcon: Icon(Icons.description, size: 28),
            label: 'Prescriptions',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person, size: 28),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}
