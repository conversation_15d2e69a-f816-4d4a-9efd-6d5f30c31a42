import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/cart/domain/models/cart_model.dart';

class CartService {
  final ApiService _apiService;

  CartService({
    ApiService? apiService,
  }) : _apiService = apiService ?? ApiService();

  // Add item to cart
  Future<CartModel> addToCart(String userId, String productId, int quantity) async {
    try {
      // First check if the cart already exists and get current items
      CartModel currentCart;
      try {
        currentCart = await getCartItems(userId);
      } catch (e) {
        // If cart doesn't exist, create an empty one
        currentCart = CartModel(
          id: '',
          userId: userId,
          items: [],
        );
      }

      // Make the API call to add the item
      final response = await _apiService.post(
        '${AppConstants.shopCartEndpoint}/add',
        data: {
          'userId': userId,
          'productId': productId,
          'quantity': quantity,
        },
      );

      if (response['success'] == true) {
        // Parse the response to get the updated cart
        return CartModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to add item to cart');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get cart items
  Future<CartModel> getCartItems(String userId) async {
    try {
      print('Getting cart for user: $userId');
      final response = await _apiService.get(
        '${AppConstants.shopCartEndpoint}/get/$userId',
      );

      if (response['success'] == true) {
        return CartModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get cart items');
      }
    } catch (e) {
      print('Error getting cart: $e');
      // Create an empty cart model if there's an error
      if (e.toString().contains('404')) {
        print('Creating empty cart for user: $userId');
        return CartModel(
          id: '',
          userId: userId,
          items: [],
        );
      }
      rethrow;
    }
  }

  // Update cart item quantity
  Future<CartModel> updateCartItemQuantity(String userId, String productId, int quantity) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.shopCartEndpoint}/update-cart',
        data: {
          'userId': userId,
          'productId': productId,
          'quantity': quantity,
        },
      );

      if (response['success'] == true) {
        return CartModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update cart item');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Remove item from cart
  Future<CartModel> removeFromCart(String userId, String productId) async {
    try {
      final response = await _apiService.delete(
        '${AppConstants.shopCartEndpoint}/$userId/$productId',
      );

      if (response['success'] == true) {
        return CartModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to remove item from cart');
      }
    } catch (e) {
      rethrow;
    }
  }
}
