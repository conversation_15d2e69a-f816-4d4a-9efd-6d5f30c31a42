import 'package:medicine_shop/core/utils/number_utils.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';

class CartItemModel {
  final String productId;
  final String image;
  final String title;
  final double price;
  final double salePrice;
  int quantity;

  CartItemModel({
    required this.productId,
    required this.image,
    required this.title,
    required this.price,
    required this.salePrice,
    required this.quantity,
  });

  factory CartItemModel.fromJson(Map<String, dynamic> json) {
    return CartItemModel(
      productId: json['productId'] ?? '',
      image: json['image'] ?? '',
      title: json['title'] ?? '',
      price: NumberUtils.parseDouble(json['price']),
      salePrice: NumberUtils.parseDouble(json['salePrice']),
      quantity: NumberUtils.parseInt(json['quantity']),
    );
  }

  factory CartItemModel.fromProduct(ProductModel product, int quantity) {
    return CartItemModel(
      productId: product.id,
      image: product.image,
      title: product.title,
      price: product.price,
      salePrice: product.salePrice,
      quantity: quantity,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'image': image,
      'title': title,
      'price': price,
      'salePrice': salePrice,
      'quantity': quantity,
    };
  }

  bool get isOnSale => salePrice > 0 && salePrice < price;
  double get finalPrice => isOnSale ? salePrice : price;
  double get totalPrice => finalPrice * quantity;

  // Create a copy with updated fields
  CartItemModel copyWith({
    String? productId,
    String? image,
    String? title,
    double? price,
    double? salePrice,
    int? quantity,
  }) {
    return CartItemModel(
      productId: productId ?? this.productId,
      image: image ?? this.image,
      title: title ?? this.title,
      price: price ?? this.price,
      salePrice: salePrice ?? this.salePrice,
      quantity: quantity ?? this.quantity,
    );
  }
}

class CartModel {
  final String id;
  final String userId;
  final List<CartItemModel> items;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CartModel({
    required this.id,
    required this.userId,
    required this.items,
    this.createdAt,
    this.updatedAt,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) {
    return CartModel(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => CartItemModel.fromJson(item))
              .toList() ??
          [],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'items': items.map((item) => item.toJson()).toList(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  double get subtotal => items.fold(0, (sum, item) => sum + item.totalPrice);
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);
  bool get isEmpty => items.isEmpty;
}
