import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/widgets/bottom_nav_app_bar.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';

class PrescriptionScreen extends StatefulWidget {
  const PrescriptionScreen({super.key});

  @override
  State<PrescriptionScreen> createState() => _PrescriptionScreenState();
}

class _PrescriptionScreenState extends State<PrescriptionScreen> {
  @override
  void initState() {
    super.initState();

    // Load user prescriptions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshPrescriptions();
    });
  }

  // Method to refresh prescriptions
  void _refreshPrescriptions() {
    if (!mounted) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

    if (authProvider.isAuthenticated && authProvider.user != null) {
      prescriptionProvider.loadUserPrescriptions(authProvider.user!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BottomNavAppBar(
        title: 'My Prescriptions',
        // Title is left-aligned by default
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.pushNamed(context, '/profile/prescription-history');
            },
            tooltip: 'Prescription History',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshPrescriptions,
            tooltip: 'Refresh prescriptions',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to prescription upload screen
          Navigator.pushNamed(context, '/profile/upload-prescription').then((_) {
            // Refresh prescriptions when returning from upload screen
            if (mounted) {
              _refreshPrescriptions();
            }
          });
        },
        backgroundColor: ScreenConstants.primaryGradientStart,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: Consumer2<AuthProvider, PrescriptionProvider>(
        builder: (context, authProvider, prescriptionProvider, child) {
          if (!authProvider.isAuthenticated) {
            return const Center(
              child: Text('Please login to view your prescriptions'),
            );
          }

          if (prescriptionProvider.status == PrescriptionStatus.loading) {
            return const LoadingIndicator(message: 'Loading prescriptions...');
          }

          if (prescriptionProvider.status == PrescriptionStatus.error) {
            return CustomErrorWidget(
              message: prescriptionProvider.errorMessage ?? 'Failed to load prescriptions',
              onRetry: () {
                if (authProvider.user != null) {
                  prescriptionProvider.loadUserPrescriptions(authProvider.user!);
                }
              },
            );
          }

          final prescriptions = prescriptionProvider.prescriptions;

          if (prescriptions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.description_outlined,
                    size: 80,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No prescriptions found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Your prescriptions will appear here',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 24),
                  CustomButton(
                    text: 'Upload Prescription',
                    onPressed: () {
                      Navigator.pushNamed(context, '/profile/upload-prescription').then((_) {
                        // Refresh prescriptions when returning from upload screen
                        if (mounted) {
                          _refreshPrescriptions();
                        }
                      });
                    },
                    type: ButtonType.primary,
                    icon: Icons.upload_file,
                    isFullWidth: false,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: prescriptions.length,
            itemBuilder: (context, index) {
              final prescription = prescriptions[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status Bar
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        color: _getStatusColor(prescription.status),
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _getStatusIcon(prescription.status),
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _getStatusText(prescription.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Prescription Details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date
                          Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Text(
                                'Uploaded on ${DateFormat('MMM dd, yyyy').format(prescription.createdAt)}',
                                style: const TextStyle(
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Prescription Image
                          GestureDetector(
                            onTap: () {
                              // Show full-screen image
                              showDialog(
                                context: context,
                                builder: (context) => Dialog(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      AppBar(
                                        title: const Text('Prescription'),
                                        automaticallyImplyLeading: false,
                                        actions: [
                                          IconButton(
                                            icon: const Icon(Icons.close),
                                            onPressed: () => Navigator.pop(context),
                                          ),
                                        ],
                                      ),
                                      InteractiveViewer(
                                        panEnabled: true,
                                        boundaryMargin: const EdgeInsets.all(20),
                                        minScale: 0.5,
                                        maxScale: 4,
                                        child: CachedNetworkImage(
                                          imageUrl: _getFullImageUrl(prescription.imageUrl),
                                          fit: BoxFit.contain,
                                          placeholder: (context, url) => const Center(
                                            child: CircularProgressIndicator(),
                                          ),
                                          errorWidget: (context, url, error) => Container(
                                            height: 300,
                                            color: Colors.grey[200],
                                            child: const Center(
                                              child: Icon(Icons.error, size: 50, color: Colors.grey),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedNetworkImage(
                                imageUrl: _getFullImageUrl(prescription.imageUrl),
                                height: 200,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  height: 200,
                                  color: Colors.grey[100],
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  height: 200,
                                  color: Colors.grey[200],
                                  child: const Center(
                                    child: Icon(Icons.error, size: 50, color: Colors.grey),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Notes
                          if (prescription.notes.isNotEmpty) ...[
                            const Text(
                              'Notes:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(prescription.notes),
                            const SizedBox(height: 16),
                          ],

                          // Actions
                          if (prescription.status.toLowerCase() == 'completed') ...[
                            CustomButton(
                              text: 'Order Medicines',
                              onPressed: () {
                                // Navigate to products screen
                                Navigator.pushNamed(context, '/products');
                              },
                              type: ButtonType.primary,
                              icon: Icons.shopping_cart,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.pending;
      case 'processing':
        return Icons.sync;
      case 'completed':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending Review';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  // Helper method to get full image URL
  String _getFullImageUrl(String imageUrl) {
    // If the URL is empty or null, return a placeholder
    if (imageUrl.isEmpty) {
      return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If it's already a Cloudinary URL, use it directly
    if (imageUrl.contains('cloudinary.com')) {
      // For Cloudinary raw URLs, convert them to image URLs if needed
      if (imageUrl.contains('/raw/upload/')) {
        return imageUrl.replaceAll('/raw/upload/', '/image/upload/');
      }
      return imageUrl;
    }

    // If it's any other full URL, use it directly
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Handle relative paths
    if (imageUrl.startsWith('/uploads/')) {
      return '${AppConstants.baseUrl.replaceAll('/api', '')}$imageUrl';
    }

    if (imageUrl.startsWith('uploads/')) {
      return '${AppConstants.baseUrl.replaceAll('/api', '')}/$imageUrl';
    }

    // For any other case, try to construct a valid URL
    return '${AppConstants.baseUrl.replaceAll('/api', '')}/uploads/$imageUrl';
  }
}
