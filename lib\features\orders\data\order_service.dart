import 'package:flutter/foundation.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/orders/domain/models/order_model.dart';

class OrderService {
  final ApiService _apiService;

  OrderService({
    ApiService? apiService,
  }) : _apiService = apiService ?? ApiService();

  // Create a new order
  Future<Map<String, dynamic>> createOrder(OrderModel order) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.shopOrderEndpoint}/create',
        data: order.toCreateJson(),
      );

      if (response['success'] == true) {
        final result = {
          'orderId': response['orderId'],
        };

        // Handle Razorpay response
        if (response['razorpayOrderId'] != null) {
          result['razorpayOrderId'] = response['razorpayOrderId'];
        }

        if (response['amount'] != null) {
          result['amount'] = response['amount'];
        }

        // Handle PayPal response
        if (response['approvalURL'] != null) {
          result['approvalURL'] = response['approvalURL'];
        }

        return result;
      } else {
        throw Exception(response['message'] ?? 'Failed to create order');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Capture payment (legacy method, kept for backward compatibility)
  Future<OrderModel> capturePayment(String orderId, String paymentId, String payerId, {String? signature}) async {
    try {
      final data = {
        'orderId': orderId,
        'paymentId': paymentId,
        'payerId': payerId,
      };

      // Add signature for Razorpay payments
      if (signature != null) {
        data['signature'] = signature;
      }

      final response = await _apiService.post(
        '${AppConstants.shopOrderEndpoint}/capture',
        data: data,
      );

      if (response['success'] == true) {
        return OrderModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to capture payment');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Capture Razorpay payment with named parameters for clarity
  Future<OrderModel> captureRazorpayPayment({
    required String orderId,
    required String razorpayPaymentId,
    required String razorpayOrderId,
    required String razorpaySignature,
  }) async {
    try {
      debugPrint('📦 Sending Razorpay payment capture request for order: $orderId');

      // Prepare data with EXACT parameter names expected by the server
      // The server expects these exact parameter names for verification
      final data = {
        'orderId': orderId,
        'razorpay_payment_id': razorpayPaymentId,
        'razorpay_order_id': razorpayOrderId,
        'razorpay_signature': razorpaySignature,
      };

      debugPrint('📦 Payment capture data: $data');

      // Add retry logic for better reliability
      int maxRetries = 3;
      int retryCount = 0;
      Exception? lastException;

      while (retryCount < maxRetries) {
        try {
          final response = await _apiService.post(
            '${AppConstants.shopOrderEndpoint}/capture',
            data: data,
          );

          debugPrint('📦 Payment capture response: $response');

          if (response['success'] == true) {
            return OrderModel.fromJson(response['data']);
          } else {
            throw Exception(response['message'] ?? 'Failed to capture Razorpay payment');
          }
        } catch (e) {
          lastException = e is Exception ? e : Exception(e.toString());
          debugPrint('❌ Error in captureRazorpayPayment (attempt ${retryCount + 1}): $e');
          retryCount++;

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
            debugPrint('🔄 Retrying payment capture (attempt ${retryCount + 1})...');
          }
        }
      }

      // If we get here, all retries failed
      throw lastException ?? Exception('Failed to capture Razorpay payment after $maxRetries attempts');
    } catch (e) {
      debugPrint('❌ Error in captureRazorpayPayment: $e');
      rethrow;
    }
  }

  // Get all orders for a user
  Future<List<OrderModel>> getUserOrders(String userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopOrderEndpoint}/list/$userId',
      );

      if (response['success'] == true) {
        final List<dynamic> ordersJson = response['data'];
        return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get orders');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get order details
  Future<OrderModel> getOrderDetails(String orderId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopOrderEndpoint}/details/$orderId',
      );

      if (response['success'] == true) {
        return OrderModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get order details');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Admin: Get all orders
  Future<List<OrderModel>> getAllOrders() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.adminOrdersEndpoint}/get',
      );

      if (response['success'] == true) {
        final List<dynamic> ordersJson = response['data'];
        return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get all orders');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Admin: Update order status
  Future<OrderModel> updateOrderStatus(String orderId, String status) async {
    try {
      debugPrint('Updating order status: $orderId to $status');
      final response = await _apiService.put(
        '${AppConstants.adminOrdersEndpoint}/update/$orderId',
        data: {
          'orderStatus': status,
        },
      );

      debugPrint('Update order status response: $response');
      if (response['success'] == true && response['data'] != null) {
        return OrderModel.fromJson(response['data']);
      } else if (response['success'] == true) {
        // If success is true but data is null, fetch the updated order details
        debugPrint('Success but no data, fetching order details');
        return await getOrderDetails(orderId);
      } else {
        throw Exception(response['message'] ?? 'Failed to update order status');
      }
    } catch (e) {
      debugPrint('Error updating order status: $e');
      rethrow;
    }
  }

  // Admin: Get unassigned orders
  Future<List<OrderModel>> getUnassignedOrders() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.adminOrdersEndpoint}/unassigned',
      );

      if (response['success'] == true) {
        final List<dynamic> ordersJson = response['data'];
        debugPrint("✅ Successfully fetched ${ordersJson.length} unassigned orders");
        return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
      } else {
        debugPrint("❌ API returned success=false: ${response['message']}");
        throw Exception(response['message'] ?? 'Failed to get unassigned orders');
      }
    } catch (e) {
      debugPrint("❌ Error fetching unassigned orders: $e");
      rethrow;
    }
  }

  // Admin: Accept order
  Future<OrderModel> acceptOrder(String orderId) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.adminOrdersEndpoint}/accept/$orderId',
        data: {},
      );

      if (response['success'] == true) {
        return OrderModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to accept order');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Admin: Get accepted orders
  Future<List<OrderModel>> getAcceptedOrders() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.adminOrdersEndpoint}/accepted',
      );

      if (response['success'] == true) {
        final List<dynamic> ordersJson = response['data'];
        debugPrint("✅ Successfully fetched ${ordersJson.length} accepted orders");
        return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
      } else {
        debugPrint("❌ API returned success=false: ${response['message']}");
        throw Exception(response['message'] ?? 'Failed to get accepted orders');
      }
    } catch (e) {
      debugPrint("❌ Error fetching accepted orders: $e");
      rethrow;
    }
  }

  // Admin: Mark order as delivered
  Future<OrderModel> markOrderAsDelivered(String orderId) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.adminOrdersEndpoint}/delivered/$orderId',
        data: {},
      );

      if (response['success'] == true) {
        return OrderModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to mark order as delivered');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Admin: Reject order
  Future<OrderModel> rejectOrder(String orderId) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.adminOrdersEndpoint}/reject/$orderId',
        data: {},
      );

      if (response['success'] == true) {
        return OrderModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to reject order');
      }
    } catch (e) {
      rethrow;
    }
  }
}
