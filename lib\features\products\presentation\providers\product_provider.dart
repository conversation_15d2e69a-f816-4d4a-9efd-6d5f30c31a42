import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:medicine_shop/core/models/app_image.dart';
import 'package:medicine_shop/features/products/data/product_service.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';

enum ProductLoadingStatus {
  initial,
  loading,
  loaded,
  error,
}

class ProductProvider with ChangeNotifier {
  final ProductService _productService;

  ProductLoadingStatus _status = ProductLoadingStatus.initial;
  List<ProductModel> _products = [];
  ProductModel? _selectedProduct;
  String? _errorMessage;

  // Filters
  List<String> _selectedCategories = [];
  List<String> _selectedBrands = [];
  String _sortBy = 'price-lowtohigh';
  String _searchQuery = '';

  ProductProvider({
    ProductService? productService,
  }) : _productService = productService ?? ProductService();

  // Getters
  ProductLoadingStatus get status => _status;
  List<ProductModel> get products => _products;
  ProductModel? get selectedProduct => _selectedProduct;
  String? get errorMessage => _errorMessage;
  List<String> get selectedCategories => _selectedCategories;
  List<String> get selectedBrands => _selectedBrands;
  String get sortBy => _sortBy;
  String get searchQuery => _searchQuery;

  // Get all available categories from products
  List<String> get availableCategories {
    // First get categories from products
    final categoriesFromProducts = _products.map((product) => product.category).toSet().toList();

    // Add default medicine categories if they don't exist in products
    final defaultCategories = [
      'pain_relief',
      'vitamins',
      'first_aid',
      'cold_and_flu',
      'digestive_health',
      'personal_care',
      'baby_care',
      'medical_devices',
    ];

    // Combine and deduplicate
    final allCategories = {...categoriesFromProducts, ...defaultCategories}.toList();
    allCategories.sort();
    return allCategories;
  }

  // Get all available brands from products
  List<String> get availableBrands {
    // First get brands from products
    final brandsFromProducts = _products.map((product) => product.brand).toSet().toList();

    // Add default pharmacy brands if they don't exist in products
    final defaultBrands = [
      'generic',
      'cipla',
      'sun_pharma',
      'pfizer',
      'johnson_johnson',
      'gsk',
      'himalaya',
      'dabur',
      'other',
    ];

    // Combine and deduplicate
    final allBrands = {...brandsFromProducts, ...defaultBrands}.toList();
    allBrands.sort();
    return allBrands;
  }

  // Get filtered products
  List<ProductModel> get filteredProducts {
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      return _products.where((product) {
        return product.title.toLowerCase().contains(query) ||
               product.description.toLowerCase().contains(query) ||
               product.category.toLowerCase().contains(query) ||
               product.brand.toLowerCase().contains(query);
      }).toList();
    }

    return _products.where((product) {
      final categoryMatch = _selectedCategories.isEmpty || _selectedCategories.contains(product.category);
      final brandMatch = _selectedBrands.isEmpty || _selectedBrands.contains(product.brand);
      return categoryMatch && brandMatch;
    }).toList();
  }

  // Load products
  Future<void> loadProducts() async {
    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final products = await _productService.getProducts(
        categories: _selectedCategories.isEmpty ? null : _selectedCategories,
        brands: _selectedBrands.isEmpty ? null : _selectedBrands,
        sortBy: _sortBy,
      );

      _products = products;
      _status = ProductLoadingStatus.loaded;
    } catch (e) {
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Load product details
  Future<void> loadProductDetails(String productId) async {
    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final product = await _productService.getProductDetails(productId);
      _selectedProduct = product;
      _status = ProductLoadingStatus.loaded;
    } catch (e) {
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Search products
  Future<void> searchProducts(String query) async {
    _searchQuery = query;

    if (query.isEmpty) {
      // If query is empty, just filter the existing products
      notifyListeners();
      return;
    }

    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final products = await _productService.searchProducts(query);
      _products = products;
      _status = ProductLoadingStatus.loaded;
    } catch (e) {
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Update filters
  void updateFilters({
    List<String>? categories,
    List<String>? brands,
    String? sortBy,
  }) {
    if (categories != null) _selectedCategories = categories;
    if (brands != null) _selectedBrands = brands;
    if (sortBy != null) _sortBy = sortBy;

    loadProducts();
  }

  // Clear filters
  void clearFilters() {
    _selectedCategories = [];
    _selectedBrands = [];
    _sortBy = 'price-lowtohigh';
    _searchQuery = '';

    loadProducts();
  }

  // Clear search
  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Admin: Add a new product
  Future<bool> addProduct(ProductModel product, AppImage? image) async {
    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      debugPrint('Adding product: ${product.toJson()}');
      if (image != null) {
        debugPrint('With image: ${image.isEmpty ? 'empty' : 'not empty'}');
      } else {
        debugPrint('No image provided');
      }

      final newProduct = await _productService.addProduct(product, image);
      debugPrint('Product added successfully: ${newProduct.id}');

      // Add to the list
      _products.add(newProduct);

      _status = ProductLoadingStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding product: $e');
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Admin: Update an existing product
  Future<bool> updateProduct(ProductModel product, AppImage? image) async {
    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      debugPrint('Updating product: ${product.toJson()}');
      if (image != null) {
        debugPrint('With image: ${image.isEmpty ? 'empty' : 'not empty'}');
      } else {
        debugPrint('No new image provided, using existing image: ${product.image}');
      }

      final updatedProduct = await _productService.updateProduct(product, image);
      debugPrint('Product updated successfully: ${updatedProduct.id}');

      // Update in the list
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index >= 0) {
        _products[index] = updatedProduct;
      }

      // If this was the selected product, update it
      if (_selectedProduct?.id == product.id) {
        _selectedProduct = updatedProduct;
      }

      _status = ProductLoadingStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating product: $e');
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Load products by category
  Future<void> loadProductsByCategory(String category) async {
    try {
      // Add a timeout to prevent hanging
      final products = await _productService.getProducts(
        categories: [category],
      ).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          debugPrint('Timeout loading products by category: $category');
          return []; // Return empty list on timeout
        },
      );

      // Only update the products list, don't change the status
      // This is to avoid affecting the UI when loading related products
      _products = products;
      notifyListeners();
    } catch (e) {
      // Silently fail, don't update status
      debugPrint('Error loading products by category: $e');
      // Set products to empty list to avoid showing stale data
      _products = [];
      notifyListeners();
    }
  }

  // Admin: Delete a product
  Future<bool> deleteProduct(String productId) async {
    _status = ProductLoadingStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _productService.deleteProduct(productId);

      if (success) {
        // Remove from the list
        _products.removeWhere((p) => p.id == productId);

        // If this was the selected product, clear it
        if (_selectedProduct?.id == productId) {
          _selectedProduct = null;
        }
      }

      _status = ProductLoadingStatus.loaded;
      notifyListeners();
      return success;
    } catch (e) {
      _status = ProductLoadingStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }
}
