{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "B:\\project\\webapp\\client\\android\\app\\.cxx\\Debug\\6gj514m1\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "B:\\project\\webapp\\client\\android\\app\\.cxx\\Debug\\6gj514m1\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}