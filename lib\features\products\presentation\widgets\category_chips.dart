import 'package:flutter/material.dart';
import 'package:medicine_shop/core/theme/app_theme.dart'; // Keep this import for theme colors

class CategoryChips extends StatelessWidget {
  final List<CategoryItem> categories;
  final String? selectedCategory;
  final Function(String?) onCategorySelected;

  const CategoryChips({
    super.key,
    required this.categories,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120, // Reduced height to remove extra bottom space
      alignment: Alignment.center,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(categories.length, (index) {
          final category = categories[index];
          final isSelected = selectedCategory == category.name;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            child: GestureDetector(
              onTap: () => onCategorySelected(
                isSelected ? null : category.name,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Simple container with no border, just grey background
                  Container(
                    width: 100, // Increased width to accommodate padding
                    height: 80, // Keep the same height
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                      // No border
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 5), // Add horizontal padding
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10), // Adjusted for padding
                      child: Image.asset(
                        category.imagePath,
                        width: 90, // Keep image width the same
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          width: 90,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(10), // Match the ClipRRect
                          ),
                          child: Icon(
                            category.fallbackIcon,
                            color: Colors.grey[600],
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Text with selection indicator line below
                  Column(
                    children: [
                      SizedBox(
                        width: 100, // Match container width
                        child: Text(
                          category.name,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade700,
                          ),
                        ),
                      ),
                      // Small thick horizontal line for selected categories
                      if (isSelected)
                        Container(
                          margin: const EdgeInsets.only(top: 2),
                          width: 20, // Small centered line
                          height: 3, // Thick line
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(1.5),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          );
          }),
        ),
      ),
    );
  }
}

class CategoryItem {
  final String name;
  final String imagePath;
  final IconData fallbackIcon;

  CategoryItem({
    required this.name,
    required this.imagePath,
    required this.fallbackIcon,
  });
}

// Predefined categories with local images from assets
class MedicineCategories {
  static List<CategoryItem> getCategories() {
    return [
      CategoryItem(
        name: 'All',
        imagePath: 'assets/CategoryImages/All.png',
        fallbackIcon: Icons.apps,
      ),
      CategoryItem(
        name: 'Medicines',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.medication,
      ),
      CategoryItem(
        name: 'Vitamins',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.emoji_food_beverage,
      ),
      CategoryItem(
        name: 'Personal Care',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.face,
      ),
      CategoryItem(
        name: 'Baby Care',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.child_care,
      ),
      CategoryItem(
        name: 'Health Devices',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.device_thermostat,
      ),
      CategoryItem(
        name: 'Ayurvedic',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.spa,
      ),
      CategoryItem(
        name: 'Fitness',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.fitness_center,
      ),
      CategoryItem(
        name: 'Covid Essentials',
        imagePath: 'assets/CategoryImages/Vitamins.png',
        fallbackIcon: Icons.health_and_safety,
      ),
    ];
  }
}
