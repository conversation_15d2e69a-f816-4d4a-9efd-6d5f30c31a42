import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;

/// A cross-platform image model that works on both web and mobile
class AppImage {
  /// The file for mobile platforms
  final File? file;
  
  /// The bytes for web platform
  final Uint8List? bytes;
  
  /// The name of the file
  final String? name;
  
  /// The mime type of the image
  final String? mimeType;

  const AppImage({
    this.file,
    this.bytes,
    this.name,
    this.mimeType,
  });

  /// Returns true if the image is not null
  bool get isNotEmpty => file != null || bytes != null;

  /// Returns true if the image is null
  bool get isEmpty => !isNotEmpty;

  /// Creates an AppImage from a File (for mobile)
  static AppImage fromFile(File file) {
    return AppImage(
      file: file,
      name: file.path.split('/').last,
    );
  }

  /// Creates an AppImage from bytes (for web)
  static AppImage fromBytes(Uint8List bytes, {String? name, String? mimeType}) {
    return AppImage(
      bytes: bytes,
      name: name ?? 'image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      mimeType: mimeType ?? 'image/jpeg',
    );
  }

  /// Returns true if the current platform is web
  static bool get isWeb => kIsWeb;
}
