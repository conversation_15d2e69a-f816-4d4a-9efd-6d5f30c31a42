import 'package:flutter/material.dart';
import 'package:medicine_shop/features/auth/presentation/screens/login_screen.dart';
import 'package:medicine_shop/features/auth/presentation/screens/register_screen.dart';

enum AuthScreenType {
  login,
  register,
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  AuthScreenType _currentScreen = AuthScreenType.login;

  void _navigateToLogin() {
    setState(() {
      _currentScreen = AuthScreenType.login;
    });
  }

  void _navigateToRegister() {
    setState(() {
      _currentScreen = AuthScreenType.register;
    });
  }

  @override
  Widget build(BuildContext context) {
    switch (_currentScreen) {
      case AuthScreenType.login:
        return LoginScreen(
          onRegisterPressed: _navigateToRegister,
        );
      case AuthScreenType.register:
        return RegisterScreen(
          onLoginPressed: _navigateToLogin,
        );
    }
  }
}
