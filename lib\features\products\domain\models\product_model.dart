import 'package:medicine_shop/core/utils/number_utils.dart';

class ProductModel {
  final String id;
  final String image;
  final String title;
  final String description;
  final String category;
  final String brand;
  final double price;
  final double salePrice;
  final int totalStock;
  final double averageReview;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ProductModel({
    required this.id,
    required this.image,
    required this.title,
    required this.description,
    required this.category,
    required this.brand,
    required this.price,
    required this.salePrice,
    required this.totalStock,
    required this.averageReview,
    this.createdAt,
    this.updatedAt,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['_id'] ?? '',
      image: json['image'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      brand: json['brand'] ?? '',
      price: NumberUtils.parseDouble(json['price']),
      salePrice: NumberUtils.parseDouble(json['salePrice']),
      totalStock: NumberUtils.parseInt(json['totalStock']),
      averageReview: NumberUtils.parseDouble(json['averageReview']),
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'image': image,
      'title': title,
      'description': description,
      'category': category,
      'brand': brand,
      'price': price,
      'salePrice': salePrice,
      'totalStock': totalStock,
      'averageReview': averageReview,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  bool get isOnSale => salePrice > 0 && salePrice < price;
  double get discountPercentage => isOnSale ? ((price - salePrice) / price * 100).roundToDouble() : 0;
  double get finalPrice => isOnSale ? salePrice : price;
  bool get isInStock => totalStock > 0;
}
