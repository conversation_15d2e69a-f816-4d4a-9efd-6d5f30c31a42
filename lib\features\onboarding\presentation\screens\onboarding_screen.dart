import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/features/auth/presentation/screens/auth_wrapper.dart';
import 'package:medicine_shop/features/onboarding/presentation/widgets/animated_background.dart';
import 'package:medicine_shop/features/onboarding/presentation/widgets/animated_illustration.dart';
import 'package:medicine_shop/features/onboarding/presentation/widgets/animated_page_indicator.dart';
import 'package:medicine_shop/features/onboarding/presentation/widgets/page_transition_effect.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLastPage = false;

  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonScaleAnimation;

  @override
  void initState() {
    super.initState();

    // Set system UI overlay style for a more immersive experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    // Initialize animations
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _buttonScaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _buttonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Ensure the first page is fully loaded and visible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // Force a rebuild after the first frame to ensure content is visible
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _buttonAnimationController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    // Save that onboarding has been completed
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_onboarding', true);

    if (!mounted) return;

    // Navigate to auth screen
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const AuthWrapper(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeOutCubic;

          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);

          return SlideTransition(position: offsetAnimation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Animated background
          AnimatedBackground(pageIndex: _currentPage),

          // Main content
          SafeArea(
            child: Column(
              children: [
                // Page content with enhanced PageView
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    physics: const ClampingScrollPhysics(), // Smoother scrolling
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                        _isLastPage = index == onboardingData.length - 1;
                      });
                    },
                    itemCount: onboardingData.length,
                    itemBuilder: (context, index) {
                      return PageTransitionEffect(
                        pageController: _pageController,
                        index: index,
                        itemCount: onboardingData.length,
                        child: _buildPage(
                          title: onboardingData[index]['title'],
                          description: onboardingData[index]['description'],
                          animationPath: onboardingData[index]['animation'],
                          color: onboardingData[index]['color'],
                        ),
                      );
                    },
                  ),
                ),

                // Bottom navigation
                Container(
                  padding: const EdgeInsets.fromLTRB(30, 20, 30, 40),
                  child: Column(
                    children: [
                      // Enhanced animated page indicator (dots only)
                      AnimatedPageIndicator(
                        pageController: _pageController,
                        currentPage: _currentPage,
                        count: onboardingData.length,
                      ),

                      const SizedBox(height: 20),

                      // Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Skip button
                          _isLastPage
                          ? const SizedBox(width: 100)
                          : TextButton(
                              onPressed: _completeOnboarding,
                              style: TextButton.styleFrom(
                                foregroundColor: ScreenConstants.primaryGradientStart,
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                              ),
                              child: Text(
                                'Skip',
                                style: ScreenConstants.skipButtonTextStyle,
                              ),
                            ),

                          // Next/Get Started button
                          GestureDetector(
                            onTapDown: (_) => _buttonAnimationController.forward(),
                            onTapUp: (_) {
                              _buttonAnimationController.reverse();
                              if (_isLastPage) {
                                _completeOnboarding();
                              } else {
                                _pageController.nextPage(
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            onTapCancel: () => _buttonAnimationController.reverse(),
                            child: AnimatedBuilder(
                              animation: _buttonAnimationController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _buttonScaleAnimation.value,
                                  child: child,
                                );
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: _isLastPage ? 30 : 24,
                                  vertical: 15
                                ),
                                decoration: ScreenConstants.buttonDecoration,
                                child: Row(
                                  children: [
                                    Text(
                                      _isLastPage ? 'Get Started' : 'Next',
                                      style: ScreenConstants.buttonTextStyle,
                                    ),
                                    const SizedBox(width: 8),
                                    const Icon(
                                      Icons.arrow_forward,
                                      color: Colors.white,
                                      size: 18,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get the appropriate icon based on the page
  IconData _getIconForPage(String animationPath) {
    if (animationPath.contains('browse')) {
      return Icons.search;
    } else if (animationPath.contains('delivery')) {
      return Icons.local_shipping;
    } else if (animationPath.contains('tracking')) {
      return Icons.monitor_heart;
    }
    return Icons.medical_services;
  }

  Widget _buildPage({
    required String title,
    required String description,
    required String animationPath,
    required Color color, // Kept for compatibility but not used
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available height to ensure content fits
        final availableHeight = constraints.maxHeight;

        // Adjust sizes based on available height
        final illustrationSize = availableHeight * 0.35;
        final titleSpacing = availableHeight * 0.04;
        final descriptionSpacing = availableHeight * 0.02;

        return Center(
          child: SingleChildScrollView(
            physics: const NeverScrollableScrollPhysics(), // Prevent scrolling
            child: Container(
              height: availableHeight,
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Enhanced animated illustration with constrained size
                  SizedBox(
                    height: illustrationSize,
                    child: AnimatedIllustration(
                      title: title,
                      icon: _getIconForPage(animationPath),
                    ),
                  ),

                  SizedBox(height: titleSpacing),

                  // Title with enhanced style
                  Text(
                    title,
                    style: ScreenConstants.titleTextStyle,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: descriptionSpacing),

                  // Description in container with enhanced style
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                    decoration: ScreenConstants.descriptionContainerDecoration,
                    child: Text(
                      description,
                      style: ScreenConstants.descriptionTextStyle,
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    );
  }


}

// Enhanced custom painter for particles with glow effects
class ParticlesPainter extends CustomPainter {
  final Color color;
  final int particleCount;
  final List<Particle> particles = [];

  ParticlesPainter({required this.color, required this.particleCount}) {
    // Initialize particles with random positions and sizes
    for (int i = 0; i < particleCount; i++) {
      particles.add(Particle.random());
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Draw particles with enhanced visual effects
    for (var particle in particles) {
      final position = Offset(
        particle.x * size.width,
        particle.y * size.height,
      );

      // Base particle
      final particlePaint = Paint()
        ..color = color.withAlpha((particle.alpha * 255).toInt())
        ..style = PaintingStyle.fill;

      // Draw the particle
      final particleSize = particle.size * (particle.isGlowing ? 4 : 3);
      canvas.drawCircle(
        position,
        particleSize,
        particlePaint,
      );

      // Add glow effect for special particles
      if (particle.isGlowing) {
        // Outer glow
        final glowPaint = Paint()
          ..color = color.withAlpha((particle.alpha * 0.5 * 255).toInt())
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          position,
          particleSize * 1.8,
          glowPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Enhanced Particle class for the animated background
class Particle {
  final double x;
  final double y;
  final double size;
  final double alpha;
  final bool isGlowing;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.alpha,
    required this.isGlowing,
  });

  // Create a random particle with improved randomization
  factory Particle.random() {
    // Create some particles that glow (about 30%)
    final bool isGlowing = _randomDouble(0, 1) < 0.3;

    return Particle(
      x: _randomDouble(0, 1),
      y: _randomDouble(0, 1),
      // Vary the size more for visual interest
      size: _randomDouble(0.005, isGlowing ? 0.04 : 0.025),
      // Glowing particles are more visible
      alpha: _randomDouble(isGlowing ? 0.4 : 0.1, isGlowing ? 0.8 : 0.5),
      isGlowing: isGlowing,
    );
  }

  // Helper method to generate random doubles with improved randomization
  static double _randomDouble(double minValue, double maxValue) {
    // Using microseconds for better randomization
    final random = (DateTime.now().microsecondsSinceEpoch % 10000) / 10000;
    return minValue + (maxValue - minValue) * random;
  }
}

// Onboarding data
final List<Map<String, dynamic>> onboardingData = [
  {
    'title': 'Find the Right Medicine',
    'description': 'Browse through our extensive catalog of medicines, vitamins, and healthcare products.',
    'animation': 'assets/animations/medicine_browse.json',
    'color': const Color.fromARGB(255, 13, 69, 108), // Using consistent color for all pages
  },
  {
    'title': 'Fast Delivery',
    'description': 'Get your medicines delivered to your doorstep quickly and safely.',
    'animation': 'assets/animations/medicine_delivery.json',
    'color': ScreenConstants.primaryGradientStart, // Using consistent color for all pages
  },
  {
    'title': 'Health Tracking',
    'description': 'Keep track of your prescriptions and medication schedules with ease.',
    'animation': 'assets/animations/medicine_tracking.json',
    'color': ScreenConstants.primaryGradientStart, // Using consistent color for all pages
  },
];
