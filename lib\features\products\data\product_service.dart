
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/models/app_image.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:flutter/material.dart';

class ProductService {
  final ApiService _apiService;

  ProductService({
    ApiService? apiService,
  }) : _apiService = apiService ?? ApiService();

  // Get all products with optional filters
  Future<List<ProductModel>> getProducts({
    List<String>? categories,
    List<String>? brands,
    String? sortBy,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (categories != null && categories.isNotEmpty) {
        queryParams['category'] = categories.join(',');
      }

      if (brands != null && brands.isNotEmpty) {
        queryParams['brand'] = brands.join(',');
      }

      if (sortBy != null) {
        queryParams['sortBy'] = sortBy;
      }

      final response = await _apiService.get(
        '${AppConstants.shopProductsEndpoint}/get',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> productsJson = response['data'];
        return productsJson.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to load products');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get product details
  Future<ProductModel> getProductDetails(String productId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopProductsEndpoint}/get/$productId',
      );

      if (response['success'] == true) {
        return ProductModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to load product details');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Search products
  Future<List<ProductModel>> searchProducts(String keyword) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopSearchEndpoint}/$keyword',
      );

      if (response['success'] == true) {
        final List<dynamic> productsJson = response['data'];
        return productsJson.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to search products');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get product reviews
  Future<List<Map<String, dynamic>>> getProductReviews(String productId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopReviewEndpoint}/$productId',
      );

      if (response['success'] == true) {
        final List<dynamic> reviewsJson = response['data'];
        return reviewsJson.cast<Map<String, dynamic>>();
      } else {
        throw Exception(response['message'] ?? 'Failed to load product reviews');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Add product review
  Future<bool> addProductReview(String productId, String userId, double rating, String comment) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.shopReviewEndpoint}/add',
        data: {
          'productId': productId,
          'userId': userId,
          'rating': rating,
          'comment': comment,
        },
      );

      return response['success'] == true;
    } catch (e) {
      rethrow;
    }
  }

  // Admin: Add a new product
  Future<ProductModel> addProduct(ProductModel product, AppImage? image) async {
    try {
      String imageUrl = '';

      // First upload the image if provided
      if (image != null && image.isNotEmpty) {
        try {
          imageUrl = await _uploadProductImage(image);
          debugPrint('Image uploaded successfully: $imageUrl');
        } catch (e) {
          debugPrint('Error uploading image in addProduct: $e');
          rethrow;
        }
      }

      // Then create the product
      final productData = product.toJson();
      if (imageUrl.isNotEmpty) {
        productData['image'] = imageUrl;
      }

      // Remove _id field if it's empty (for new products)
      if (productData.containsKey('_id') && productData['_id'] == '') {
        productData.remove('_id');
      }

      // Remove createdAt and updatedAt fields for new products
      productData.remove('createdAt');
      productData.remove('updatedAt');

      debugPrint('Adding product with data: $productData');

      final response = await _apiService.post(
        '${AppConstants.adminProductsEndpoint}/add',
        data: productData,
      );

      debugPrint('Add product response: $response');

      if (response['success'] == true) {
        return ProductModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to add product');
      }
    } catch (e) {
      debugPrint('Error adding product: $e');
      rethrow;
    }
  }

  // Admin: Update an existing product
  Future<ProductModel> updateProduct(ProductModel product, AppImage? image) async {
    try {
      String imageUrl = product.image;

      // First upload the image if provided
      if (image != null && image.isNotEmpty) {
        try {
          imageUrl = await _uploadProductImage(image);
          debugPrint('Image uploaded successfully: $imageUrl');
        } catch (e) {
          debugPrint('Error uploading image in updateProduct: $e');
          rethrow;
        }
      }

      // Then update the product
      final productData = product.toJson();
      productData['image'] = imageUrl;

      // Use the correct endpoint path: /edit/{id} instead of /update/{id}
      final response = await _apiService.put(
        '${AppConstants.adminProductsEndpoint}/edit/${product.id}',
        data: productData,
      );

      if (response['success'] == true) {
        return ProductModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update product');
      }
    } catch (e) {
      debugPrint('Error updating product: $e');
      rethrow;
    }
  }

  // Helper method to upload an image
  Future<String> _uploadProductImage(AppImage image) async {
    try {
      if (image.isEmpty) {
        return '';
      }

      FormData formData;

      if (kIsWeb && image.bytes != null) {
        // For web platform
        try {
          debugPrint('Preparing web image upload: ${image.bytes!.length} bytes');

          // Create a multipart file directly from bytes
          formData = FormData.fromMap({
            'my_file': MultipartFile.fromBytes(
              image.bytes!,
              filename: image.name ?? 'image.jpg',
              contentType: image.mimeType != null ? MediaType.parse(image.mimeType!) : null,
            ),
          });

          debugPrint('Web image formData created successfully');
        } catch (e) {
          debugPrint('Error encoding image for web: $e');
          throw Exception('Failed to process image data for web: $e');
        }
      } else if (!kIsWeb && image.file != null) {
        // For mobile platforms
        try {
          debugPrint('Preparing mobile image upload from file: ${image.file!.path}');

          formData = FormData.fromMap({
            'my_file': await MultipartFile.fromFile(
              image.file!.path,
              filename: image.name ?? image.file!.path.split('/').last,
            ),
          });

          debugPrint('Mobile image formData created successfully');
        } catch (e) {
          debugPrint('Error creating MultipartFile from file: $e');
          throw Exception('Failed to process image file: $e');
        }
      } else {
        debugPrint('Invalid image data: kIsWeb=$kIsWeb, bytes=${image.bytes != null}, file=${image.file != null}');
        throw Exception('Invalid image data');
      }

      final response = await _apiService.post(
        '${AppConstants.adminProductsEndpoint}/upload-image',
        data: formData,
      );

      debugPrint('Image upload response: $response');

      // Check for success and result structure (Cloudinary response)
      if (response != null && response['success'] == true) {
        // Check if the response has a 'result' field (Cloudinary format)
        if (response['result'] != null) {
          // Prefer secure_url if available
          if (response['result']['secure_url'] != null) {
            debugPrint('Using secure_url from result: ${response['result']['secure_url']}');
            return response['result']['secure_url'];
          }
          // Fall back to url if secure_url is not available
          else if (response['result']['url'] != null) {
            debugPrint('Using url from result: ${response['result']['url']}');
            return response['result']['url'];
          }
        }
        // Check for the data.url format (alternative format)
        else if (response['data'] != null && response['data']['url'] != null) {
          debugPrint('Using url from data: ${response['data']['url']}');
          return response['data']['url'];
        }
      }

      // If we reach here, we couldn't find a valid URL
      final errorMsg = response != null && response['message'] != null
          ? response['message']
          : 'Failed to extract image URL from response';
      debugPrint('Error extracting URL: $errorMsg');
      debugPrint('Response structure: $response');
      throw Exception(errorMsg);
    } catch (e) {
      debugPrint('Error in _uploadProductImage: $e');
      rethrow;
    }
  }

  // Admin: Delete a product
  Future<bool> deleteProduct(String productId) async {
    try {
      final response = await _apiService.delete(
        '${AppConstants.adminProductsEndpoint}/delete/$productId',
      );

      return response['success'] == true;
    } catch (e) {
      rethrow;
    }
  }
}
