import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';

class ApiService {
  late Dio _dio;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  ApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: const Duration(seconds: 30), // Increased timeout
        receiveTimeout: const Duration(seconds: 30), // Increased timeout
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Access-Control-Allow-Origin': '*', // Allow all origins
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS', // Allow all methods
          'Access-Control-Allow-Headers': 'Origin, Content-Type, X-Auth-Token, Authorization, Cookie', // Allow all headers
        },
        // Enable cookies for authentication
        validateStatus: (status) => status! < 500,
      ),
    );

    // Enable cookies
    _dio.options.extra['withCredentials'] = true;

    // Add cookie handling
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          print('Making request to: ${options.uri}');

          // Get token from secure storage
          final token = await _secureStorage.read(key: AppConstants.tokenKey);
          if (token != null) {
            // Set token in Authorization header
            options.headers['Authorization'] = 'Bearer $token';

            // CRITICAL: Add token as a cookie - this is what the backend middleware expects
            // The backend specifically looks for a cookie named 'token'
            // This is the exact format the backend expects
            options.headers['Cookie'] = 'token=$token';

            // IMPORTANT: Also set the token as a query parameter
            // This is a workaround for servers that don't properly handle cookies
            if (options.queryParameters == null) {
              options.queryParameters = {};
            }
            options.queryParameters['token'] = token;

            print('🔑 Token added to request: ${options.method} ${options.path}');
            print('🍪 Cookie: ${options.headers['Cookie']}');
          } else {
            print('⚠️ No token available for ${options.method} request to ${options.path}');

            // Try to read token again - sometimes there's a timing issue
            await Future.delayed(Duration(milliseconds: 100));
            final retryToken = await _secureStorage.read(key: AppConstants.tokenKey);
            if (retryToken != null) {
              options.headers['Authorization'] = 'Bearer $retryToken';
              options.headers['Cookie'] = 'token=$retryToken';

              // Also set as query parameter
              if (options.queryParameters == null) {
                options.queryParameters = {};
              }
              options.queryParameters['token'] = retryToken;

              print('🔄 Retry successful - token added to request');
            }
          }

          // Enable cookies for cross-origin requests
          options.extra['withCredentials'] = true;

          return handler.next(options);
        },
        onResponse: (response, handler) async {
          print('Response status: ${response.statusCode}');
          print('Response headers: ${response.headers.map}');

          // Check for cookies in response
          final cookies = response.headers.map['set-cookie'];
          if (cookies != null && cookies.isNotEmpty) {
            print('Received cookies: $cookies');
          }
          return handler.next(response);
        },
        onError: (DioException error, handler) async {
          print('DioException in interceptor: ${error.type}');
          print('Error message: ${error.message}');
          print('Error response: ${error.response?.data}');

          // If the error is 401 Unauthorized, it might be due to token expiration
          if (error.response?.statusCode == 401) {
            // For now, we'll just pass the error through
            // In a more complex app, you could implement token refresh here
            print('Token might be expired or invalid: ${error.message}');
          }
          return handler.next(error);
        },
      ),
    );
  }

  // // GET request
  // Future<dynamic> get(String endpoint, {Map<String, dynamic>? queryParameters}) async {
  //   try {
  //     print('Making GET request to: ${_dio.options.baseUrl}$endpoint');
  //     print('Query parameters: $queryParameters');
  //     print('Headers: ${_dio.options.headers}');

  //     final response = await _dio.get(
  //       endpoint,
  //       queryParameters: queryParameters,
  //     );

  //     print('Response status: ${response.statusCode}');
  //     print('Response data: ${response.data}');

  //     return response.data;
  //   } on DioException catch (e) {
  //     print('DioException: ${e.message}');
  //     print('DioException response: ${e.response?.data}');
  //     _handleError(e);
  //   } catch (e) {
  //     print('Unknown error: $e');
  //     throw Exception('Unknown error occurred: $e');
  //   }
  // }

  // GET request with optional headers
Future<dynamic> get(
  String endpoint, {
  Map<String, dynamic>? queryParameters,
  Map<String, dynamic>? headers,
}) async {
  try {
    print('Making GET request to: ${_dio.options.baseUrl}$endpoint');

    // Get token from secure storage for cookie
    final token = await _secureStorage.read(key: AppConstants.tokenKey);

    // Create merged headers with both the passed headers and our token headers
    Map<String, dynamic> mergedHeaders = {};
    if (headers != null) {
      mergedHeaders.addAll(headers);
    }

    if (token != null) {
      // CRITICAL: Add token as cookie - this is what the backend middleware expects
      // The backend specifically looks for a cookie named 'token'
      mergedHeaders['Cookie'] = 'token=$token';

      // Also add as Authorization header for APIs that might use it
      mergedHeaders['Authorization'] = 'Bearer $token';

      // IMPORTANT: Also set the token as a query parameter
      // This is a workaround for servers that don't properly handle cookies
      if (queryParameters == null) {
        queryParameters = {};
      }
      queryParameters['token'] = token;

      print('🔑 Token added to GET request: $endpoint');
      print('🍪 Cookie: ${mergedHeaders['Cookie']}');
    } else {
      print('⚠️ No token available for GET request to $endpoint');

      // Try to read token again - sometimes there's a timing issue
      await Future.delayed(Duration(milliseconds: 100));
      final retryToken = await _secureStorage.read(key: AppConstants.tokenKey);
      if (retryToken != null) {
        mergedHeaders['Authorization'] = 'Bearer $retryToken';
        mergedHeaders['Cookie'] = 'token=$retryToken';

        // Also set as query parameter
        if (queryParameters == null) {
          queryParameters = {};
        }
        queryParameters['token'] = retryToken;

        print('🔄 Retry successful - token added to GET request');
      }
    }

    final response = await _dio.get(
      endpoint,
      queryParameters: queryParameters,
      options: Options(
        headers: mergedHeaders,
        followRedirects: true,
        validateStatus: (status) => status! < 500,
        extra: {'withCredentials': true},
      ),
    );

    print('Response status: ${response.statusCode}');
    print('Response data: ${response.data}');

    return response.data;
  } on DioException catch (e) {
    print('DioException: ${e.message}');
    print('DioException response: ${e.response?.data}');
    _handleError(e);
  } catch (e) {
    print('Unknown error: $e');
    throw Exception('Unknown error occurred: $e');
  }
}


  // POST request
  Future<dynamic> post(String endpoint, {dynamic data}) async {
    try {
      print('Making POST request to: ${_dio.options.baseUrl}$endpoint');

      // Get token from secure storage for cookie
      final token = await _secureStorage.read(key: AppConstants.tokenKey);

      // Create headers with token
      Map<String, dynamic> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token != null) {
        // CRITICAL: Add token as cookie - this is what the backend middleware expects
        // The backend specifically looks for a cookie named 'token'
        headers['Cookie'] = 'token=$token';

        // Also add as Authorization header for APIs that might use it
        headers['Authorization'] = 'Bearer $token';

        // IMPORTANT: Also set the token as a query parameter
        // This is a workaround for servers that don't properly handle cookies
        if (data is Map) {
          // If data is a Map, add token to it
          (data as Map)['token'] = token;
        } else if (data == null) {
          // If data is null, create a new Map with token
          data = {'token': token};
        }
        // If data is not a Map and not null, we can't add the token to it
        // But it will still be in the headers

        print('🔑 Token added to POST request: $endpoint');
        print('🍪 Cookie: ${headers['Cookie']}');
      } else {
        print('⚠️ No token available for POST request to $endpoint');

        // Try to read token again - sometimes there's a timing issue
        await Future.delayed(Duration(milliseconds: 100));
        final retryToken = await _secureStorage.read(key: AppConstants.tokenKey);
        if (retryToken != null) {
          headers['Authorization'] = 'Bearer $retryToken';
          headers['Cookie'] = 'token=$retryToken';

          // Also set as query parameter
          if (data is Map) {
            // If data is a Map, add token to it
            (data as Map)['token'] = retryToken;
          } else if (data == null) {
            // If data is null, create a new Map with token
            data = {'token': retryToken};
          }

          print('🔄 Retry successful - token added to POST request');
        }
      }

      // Add withCredentials option to enable cookies
      final response = await _dio.post(
        endpoint,
        data: data,
        options: Options(
          followRedirects: true,
          validateStatus: (status) => status! < 500,
          contentType: 'application/json',
          extra: {'withCredentials': true},
          headers: headers,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      // Check for cookies in response
      final cookies = response.headers.map['set-cookie'];
      if (cookies != null && cookies.isNotEmpty) {
        print('Received cookies: $cookies');
      }

      return response.data;
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      _handleError(e);
    } catch (e) {
      print('Unknown error occurred: $e');
      throw Exception('Unknown error occurred: $e');
    }
  }

  // PUT request
  Future<dynamic> put(String endpoint, {dynamic data}) async {
    try {
      print('Making PUT request to: ${_dio.options.baseUrl}$endpoint');

      // Get token from secure storage for cookie
      final token = await _secureStorage.read(key: AppConstants.tokenKey);

      // Create headers with token
      Map<String, dynamic> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token != null) {
        // CRITICAL: Add token as cookie - this is what the backend middleware expects
        // The backend specifically looks for a cookie named 'token'
        headers['Cookie'] = 'token=$token';

        // Also add as Authorization header for APIs that might use it
        headers['Authorization'] = 'Bearer $token';

        print('Added token to request headers for PUT $endpoint');
      } else {
        print('⚠️ No token available for PUT request to $endpoint');
      }

      // Add withCredentials option to enable cookies
      final response = await _dio.put(
        endpoint,
        data: data,
        options: Options(
          followRedirects: true,
          validateStatus: (status) => status! < 500,
          contentType: 'application/json',
          extra: {'withCredentials': true},
          headers: headers,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      return response.data;
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      _handleError(e);
    } catch (e) {
      print('Unknown error occurred: $e');
      throw Exception('Unknown error occurred: $e');
    }
  }

  // DELETE request
  Future<dynamic> delete(String endpoint, {dynamic data}) async {
    try {
      print('Making DELETE request to: ${_dio.options.baseUrl}$endpoint');

      // Get token from secure storage for cookie
      final token = await _secureStorage.read(key: AppConstants.tokenKey);

      // Create headers with token
      Map<String, dynamic> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token != null) {
        // CRITICAL: Add token as cookie - this is what the backend middleware expects
        // The backend specifically looks for a cookie named 'token'
        headers['Cookie'] = 'token=$token';

        // Also add as Authorization header for APIs that might use it
        headers['Authorization'] = 'Bearer $token';

        print('Added token to request headers for DELETE $endpoint');
      } else {
        print('⚠️ No token available for DELETE request to $endpoint');
      }

      // Add withCredentials option to enable cookies
      final response = await _dio.delete(
        endpoint,
        data: data,
        options: Options(
          followRedirects: true,
          validateStatus: (status) => status! < 500,
          contentType: 'application/json',
          extra: {'withCredentials': true},
          headers: headers,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      return response.data;
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      _handleError(e);
    } catch (e) {
      print('Unknown error occurred: $e');
      throw Exception('Unknown error occurred: $e');
    }
  }

  // Upload file
  Future<dynamic> uploadFile(String endpoint, File file, {String fieldName = 'file'}) async {
    try {
      print('Uploading file to: ${_dio.options.baseUrl}$endpoint');

      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(file.path),
      });

      // Get token from secure storage for cookie
      final token = await _secureStorage.read(key: AppConstants.tokenKey);

      // Create headers with token
      Map<String, dynamic> headers = {
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json',
      };

      if (token != null) {
        // CRITICAL: Add token as cookie - this is what the backend middleware expects
        // The backend specifically looks for a cookie named 'token'
        headers['Cookie'] = 'token=$token';

        // Also add as Authorization header for APIs that might use it
        headers['Authorization'] = 'Bearer $token';

        print('Added token to request headers for file upload to $endpoint');
      } else {
        print('⚠️ No token available for file upload to $endpoint');
      }

      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(
          followRedirects: true,
          validateStatus: (status) => status! < 500,
          contentType: 'multipart/form-data',
          extra: {'withCredentials': true},
          headers: headers,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      return response.data;
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      _handleError(e);
    } catch (e) {
      print('Unknown error occurred: $e');
      throw Exception('Unknown error occurred: $e');
    }
  }

  // Handle errors
  void _handleError(DioException e) {
    print('Handling DioException: ${e.type}');
    print('Error message: ${e.message}');
    print('Error response: ${e.response?.data}');

    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      throw Exception('Connection timeout. Please check your internet connection.');
    } else if (e.type == DioExceptionType.connectionError) {
      throw Exception('No internet connection. Please check your network.');
    } else if (e.type == DioExceptionType.badCertificate) {
      throw Exception('Bad SSL certificate. Please contact support.');
    } else if (e.type == DioExceptionType.badResponse) {
      throw Exception('Bad response from server. Please try again later.');
    } else if (e.type == DioExceptionType.cancel) {
      throw Exception('Request was cancelled.');
    } else if (e.response != null) {
      final statusCode = e.response!.statusCode;
      final data = e.response!.data;

      print('Response status code: $statusCode');
      print('Response data: $data');

      if (data is Map<String, dynamic> && data.containsKey('message')) {
        print('Server error message: ${data['message']}');
        throw Exception(data['message']);
      } else if (statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else if (statusCode == 403) {
        throw Exception('Forbidden. You do not have permission to access this resource.');
      } else if (statusCode == 404) {
        throw Exception('Resource not found.');
      } else if (statusCode == 500) {
        throw Exception('Server error. Please try again later.');
      } else {
        throw Exception('Error occurred: ${e.response!.statusCode}');
      }
    } else {
      throw Exception('Error occurred: ${e.message}');
    }
  }
}
