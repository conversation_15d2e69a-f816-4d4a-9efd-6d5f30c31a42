import 'package:flutter/material.dart';

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final int minCrossAxisCount;
  final int maxCrossAxisCount;
  final double childAspectRatio;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.minCrossAxisCount = 2,
    this.maxCrossAxisCount = 4,
    this.childAspectRatio = 0.75,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        // Calculate how many items can fit in a row
        int crossAxisCount = (width / 180).floor();

        // Ensure we stay within min and max limits
        crossAxisCount = crossAxisCount.clamp(minCrossAxisCount, maxCrossAxisCount);

        return GridView.builder(
          shrinkWrap: true,
          physics: physics,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            crossAxisSpacing: spacing,
            mainAxisSpacing: runSpacing,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
