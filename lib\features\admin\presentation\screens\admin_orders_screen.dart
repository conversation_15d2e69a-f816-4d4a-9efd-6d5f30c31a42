import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/network/socket_service.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/widgets/bottom_nav_app_bar.dart';
import 'package:medicine_shop/features/admin/presentation/screens/unassigned_orders_screen.dart';
import 'package:medicine_shop/features/admin/presentation/screens/order_history_screen.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';

class AdminOrdersScreen extends StatefulWidget {
  const AdminOrdersScreen({super.key});

  @override
  State<AdminOrdersScreen> createState() => _AdminOrdersScreenState();
}

class _AdminOrdersScreenState extends State<AdminOrdersScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final SocketService _socketService = SocketService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Connect to socket server
    _socketService.connect();
    debugPrint("🔌 Socket connection initialized in AdminOrdersScreen");

    // Initialize socket listeners for new orders
    _socketService.onNewOrder = (data) {
      debugPrint("📦 New order received in AdminOrdersScreen: $data");
      // Show a snackbar notification
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.shopping_bag, color: Colors.white),
              const SizedBox(width: 8),
              const Text('New order received!'),
              const Spacer(),
              TextButton(
                onPressed: () {
                  _tabController.animateTo(0); // Switch to unassigned orders tab
                  // Refresh the unassigned orders
                  Provider.of<OrderProvider>(context, listen: false).loadUnassignedOrders();
                },
                child: const Text('VIEW', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
          duration: const Duration(seconds: 5),
          backgroundColor: Colors.green,
        ),
      );
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    // Disconnect socket when screen is disposed
    _socketService.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BottomNavAppBar(
        title: 'Orders Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<OrderProvider>(context, listen: false).loadUnassignedOrders();
              Provider.of<OrderProvider>(context, listen: false).loadAcceptedOrders();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Orders refreshed'),
                  duration: const Duration(seconds: 1),
                  backgroundColor: ScreenConstants.primaryGradientStart,
                ),
              );
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Custom tab bar
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ScreenConstants.primaryGradientStart.withAlpha(30),
                  ScreenConstants.primaryGradientEnd.withAlpha(20),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: ScreenConstants.primaryGradientStart.withAlpha(20),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: const Color(0x33FFFFFF), // White with 20% opacity
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: TabBar(
                controller: _tabController,
                tabs: [
                  Tab(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: const [
                          Icon(Icons.assignment_outlined, size: 20),
                          SizedBox(width: 8),
                          Text('Unassigned', style: TextStyle(fontSize: 14)),
                        ],
                      ),
                    ),
                  ),
                  Tab(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: const [
                          Icon(Icons.history, size: 20),
                          SizedBox(width: 8),
                          Text('History', style: TextStyle(fontSize: 14)),
                        ],
                      ),
                    ),
                  ),
                ],
                indicator: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      ScreenConstants.primaryGradientStart,
                      ScreenConstants.primaryGradientEnd,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: ScreenConstants.primaryGradientStart.withAlpha(40),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                labelColor: Colors.white,
                unselectedLabelColor: ScreenConstants.descriptionColor,
                indicatorSize: TabBarIndicatorSize.tab,
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
                dividerColor: Colors.transparent,
              ),
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                // Embed the existing screens as tabs
                UnassignedOrdersScreen(isEmbedded: true),
                OrderHistoryScreen(isEmbedded: true),
              ],
            ),
          ),
        ],
      ),
    );
  }


}
