import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  final ApiService _apiService;
  final FlutterSecureStorage _secureStorage;

  AuthService({
    ApiService? apiService,
    FlutterSecureStorage? secureStorage,
  }) :
    _apiService = apiService ?? ApiService(),
    _secureStorage = secureStorage ?? const FlutterSecureStorage();

  // Register a new user
  Future<bool> register(String userName, String password, {String? phoneNumber, required String email, String role = 'user'}) async {
    try {
      // Create data object with only the fields expected by the backend
      final data = {
        'userName': userName,
        'email': email,
        'password': password,
      };

      // Note: The backend doesn't expect phoneNumber or role in the registration payload
      // so we're not including them here

      print('Sending register request with data: $data');

      final response = await _apiService.post(
        '${AppConstants.authEndpoint}/register',
        data: data,
      );

      print('Register response: $response');

      return response['success'] ?? false;
    } catch (e) {
      print('Error during registration: $e');
      rethrow;
    }
  }

  // Login user
  Future<UserModel> login(String email, String password) async {
    try {
      print('Attempting login with email: $email');

      // Create the login data
      final loginData = {
        'email': email,
        'password': password,
      };

      print('Login data: $loginData');

      // Make the login request
      final response = await _apiService.post(
        '${AppConstants.authEndpoint}/login',
        data: loginData,
      );

      print('Login response: $response');

      // Check if the response is successful
      if (response != null && response['success'] == true) {
        // Extract token and user data
        final token = response['token'];
        final userData = response['user'];

        if (token == null) {
          print('Warning: No token received in response body');
        }

        // Save token to secure storage if available
        if (token != null) {
          // First clear any existing token
          await _secureStorage.delete(key: AppConstants.tokenKey);

          // Then write the new token
          await _secureStorage.write(key: AppConstants.tokenKey, value: token);
          print('Token saved to secure storage');

          // Print the token for debugging
          print('Token value: $token');

          // Verify the token was saved correctly
          final savedToken = await _secureStorage.read(key: AppConstants.tokenKey);
          print('Token retrieved from storage: $savedToken');

          // Force a small delay to ensure the token is properly saved
          await Future.delayed(Duration(milliseconds: 300));
        }

        // Save user data to shared preferences
        final prefs = await SharedPreferences.getInstance();
        if (userData != null) {
          await prefs.setString(AppConstants.userKey, jsonEncode(userData));
          print('User data saved to shared preferences');

          // Create user model from response
          final user = UserModel.fromJson(userData);
          print('User model created: ${user.userName}, ${user.email}');

          return user;
        } else {
          throw Exception('User data not found in response');
        }
      } else {
        // Handle error response
        final errorMessage = response != null && response['message'] != null
            ? response['message']
            : 'Login failed. Please check your credentials and try again.';
        print('Login failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('Exception during login: $e');
      throw Exception('Network error. Please check your internet connection and try again.');
    }
  }

  // Check if user is logged in
  // Future<UserModel?> getCurrentUser() async {
  //   try {
  //     // Check if token exists
  //     final token = await _secureStorage.read(key: AppConstants.tokenKey);
  //     if (token == null) {
  //       return null;
  //     }

  //     // Get user data from API
  //     final response = await _apiService.get('${AppConstants.authEndpoint}/check-auth',
  //     headers: {'Authorization': 'Bearer $token'},);

  //     print(response);

  //     if (response['success'] == true) {
  //       final userData = response['user'];

  //       // Update user data in shared preferences
  //       final prefs = await SharedPreferences.getInstance();
  //       await prefs.setString(AppConstants.userKey, jsonEncode(userData));

  //       return UserModel.fromJson(userData);
  //     } else {
  //       // Clear token and user data if authentication fails
  //       await logout();
  //       return null;
  //     }
  //   } catch (e) {
  //     // Clear token and user data if there's an error
  //     await logout();
  //     return null;
  //   }
  // }
  Future<UserModel?> getCurrentUser() async {
    try {
      print('Checking current user authentication');

      // Check if token exists
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      if (token == null) {
        print('No token found in secure storage');
        return null;
      }

      print('Token found, making check-auth request');

      try {
        // Get user data from API with Authorization header
        final response = await _apiService.get(
          '${AppConstants.authEndpoint}/check-auth',
          headers: {'Authorization': 'Bearer $token'},
        );

        print('check-auth response: $response');

        if (response['success'] == true) {
          final userData = response['user'];
          print('User authenticated: $userData');

          // Save user data in SharedPreferences
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(AppConstants.userKey, jsonEncode(userData));
          print('Updated user data in SharedPreferences');

          return UserModel.fromJson(userData);
        } else {
          print('Authentication failed, logging out');
          // If not successful, log out
          await logout();
          return null;
        }
      } catch (networkError) {
        print('Network error in getCurrentUser: $networkError');
        // On network error, fall back to stored user data
        // This allows the app to work offline
        final storedUser = await getUserFromStorage();
        if (storedUser != null) {
          print('Using stored user data due to network error');
          return storedUser;
        }
        // If no stored user, rethrow to trigger logout
        rethrow;
      }
    } catch (e) {
      print('Error in getCurrentUser: $e');
      // Don't automatically log out on network errors
      // This allows the app to work offline using cached user data
      print('Falling back to stored user data');
      return await getUserFromStorage();
    }
  }


  // Get user from local storage
  Future<UserModel?> getUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userKey);

      if (userJson != null) {
        return UserModel.fromJson(jsonDecode(userJson));
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      // Call logout API
      await _apiService.post('${AppConstants.authEndpoint}/logout');
    } catch (e) {
      // Ignore errors during logout
    } finally {
      // Clear token and user data
      await _secureStorage.delete(key: AppConstants.tokenKey);
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userKey);
    }
  }
}
