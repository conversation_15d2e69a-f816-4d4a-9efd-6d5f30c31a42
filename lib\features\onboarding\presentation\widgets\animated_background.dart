import 'dart:math';
import 'package:flutter/material.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class AnimatedBackground extends StatefulWidget {
  final int pageIndex;

  const AnimatedBackground({
    Key? key,
    required this.pageIndex,
  }) : super(key: key);

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<BackgroundElement> _elements;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 12000),
    )..repeat();

    // Create random background elements
    _elements = List.generate(15, (index) => BackgroundElement.random());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    // Use consistent colors for all pages (matching splash screen)
    final Color primaryColor = ScreenConstants.primaryGradientStart;
    final Color secondaryColor = ScreenConstants.primaryGradientEnd;

    return Stack(
      children: [
        // Base gradient background
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                Colors.white,
                primaryColor.withAlpha(13), // 0.05 opacity
                secondaryColor.withAlpha(13), // 0.05 opacity
              ],
            ),
          ),
        ),

        // Large circle in background
        Positioned(
          top: -size.height * 0.15,
          right: -size.width * 0.4,
          child: Container(
            width: size.width * 0.8,
            height: size.width * 0.8,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  primaryColor.withAlpha(51), // 0.2 opacity
                  primaryColor.withAlpha(13), // 0.05 opacity
                ],
              ),
              shape: BoxShape.circle,
            ),
          ),
        ),

        // Second large circle
        Positioned(
          bottom: -size.height * 0.1,
          left: -size.width * 0.3,
          child: Container(
            width: size.width * 0.7,
            height: size.width * 0.7,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  secondaryColor.withAlpha(51), // 0.2 opacity
                  secondaryColor.withAlpha(13), // 0.05 opacity
                ],
              ),
              shape: BoxShape.circle,
            ),
          ),
        ),

        // Animated elements
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              size: Size(size.width, size.height),
              painter: BackgroundElementsPainter(
                elements: _elements,
                animation: _controller,
                primaryColor: primaryColor,
                secondaryColor: secondaryColor,
              ),
            );
          },
        ),

        // Mesh overlay for depth
        Opacity(
          opacity: 0.4,
          child: ShaderMask(
            shaderCallback: (rect) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, primaryColor.withAlpha(13)], // 0.05 opacity
              ).createShader(rect);
            },
            blendMode: BlendMode.srcOver,
            child: Container(
              width: size.width,
              height: size.height,
              color: Colors.white.withAlpha(3), // 0.01 opacity
            ),
          ),
        ),
      ],
    );
  }
}

class BackgroundElementsPainter extends CustomPainter {
  final List<BackgroundElement> elements;
  final Animation<double> animation;
  final Color primaryColor;
  final Color secondaryColor;

  BackgroundElementsPainter({
    required this.elements,
    required this.animation,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (var element in elements) {
      // Calculate animated position
      final progress = (animation.value + element.offset) % 1.0;
      final x = element.x * size.width;
      final y = element.startY + (element.endY - element.startY) * progress;

      // Calculate size with pulsing effect
      final pulsingProgress = sin(progress * pi * 2) * 0.5 + 0.5;
      final elementSize = element.size * (1 + pulsingProgress * 0.3);

      // Choose color based on element type
      final color = element.useSecondaryColor ? secondaryColor : primaryColor;
      final opacity = 0.1 + element.opacity * 0.2;

      // Draw the element
      final paint = Paint()
        ..color = color.withAlpha((opacity * 255).toInt())
        ..style = PaintingStyle.fill;

      if (element.isCircle) {
        canvas.drawCircle(
          Offset(x, y * size.height),
          elementSize * 10,
          paint,
        );
      } else {
        final rect = Rect.fromCenter(
          center: Offset(x, y * size.height),
          width: elementSize * 20,
          height: elementSize * 20,
        );
        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, Radius.circular(elementSize * 5)),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class BackgroundElement {
  final double x;
  final double startY;
  final double endY;
  final double size;
  final double opacity;
  final double offset;
  final bool isCircle;
  final bool useSecondaryColor;

  BackgroundElement({
    required this.x,
    required this.startY,
    required this.endY,
    required this.size,
    required this.opacity,
    required this.offset,
    required this.isCircle,
    required this.useSecondaryColor,
  });

  factory BackgroundElement.random() {
    final random = Random();

    return BackgroundElement(
      x: random.nextDouble(),
      startY: -0.2,
      endY: 1.2,
      size: 0.01 + random.nextDouble() * 0.04,
      opacity: 0.3 + random.nextDouble() * 0.7,
      offset: random.nextDouble(),
      isCircle: random.nextBool(),
      useSecondaryColor: random.nextBool(),
    );
  }
}
