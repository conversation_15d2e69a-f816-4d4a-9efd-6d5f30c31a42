# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "B:\\project\\webapp\\client" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "PROJECT_DIR=B:\\project\\webapp\\client"
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=B:\\project\\webapp\\client\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=B:\\project\\webapp\\client"
  "FLUTTER_TARGET=B:\\project\\webapp\\client\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=B:\\project\\webapp\\client\\.dart_tool\\package_config.json"
)
