import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/models/app_image.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/custom_text_field.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';
import 'package:medicine_shop/features/products/presentation/providers/product_provider.dart';

class ProductFormScreen extends StatefulWidget {
  final ProductModel? product;
  final bool isEditing;

  const ProductFormScreen({
    super.key,
    this.product,
    this.isEditing = false,
  });

  @override
  State<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _salePriceController = TextEditingController();
  final _stockController = TextEditingController();

  String _selectedCategory = 'pain_relief';
  String _selectedBrand = 'generic';
  AppImage? _image;
  String? _imageUrl;
  bool _isLoading = false;

  final List<String> _categories = [
    'pain_relief',
    'vitamins',
    'first_aid',
    'cold_and_flu',
    'digestive_health',
    'personal_care',
    'baby_care',
    'medical_devices',
  ];

  final List<String> _brands = [
    'generic',
    'cipla',
    'sun_pharma',
    'pfizer',
    'johnson_johnson',
    'gsk',
    'himalaya',
    'dabur',
    'other',
  ];

  @override
  void initState() {
    super.initState();

    // If editing, populate form with product data
    if (widget.isEditing && widget.product != null) {
      _titleController.text = widget.product!.title;
      _descriptionController.text = widget.product!.description;
      _priceController.text = widget.product!.price.toString();
      _salePriceController.text = widget.product!.salePrice > 0
          ? widget.product!.salePrice.toString()
          : '';
      _stockController.text = widget.product!.totalStock.toString();
      _selectedCategory = widget.product!.category;
      _selectedBrand = widget.product!.brand;
      _imageUrl = widget.product!.image;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _salePriceController.dispose();
    _stockController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1024, // Limit image size to prevent large uploads
        maxHeight: 1024,
      );

      if (pickedFile != null) {
        debugPrint('Image picked: ${pickedFile.name}, size: ${await pickedFile.length()} bytes');

        if (kIsWeb) {
          // For web platform
          try {
            final bytes = await pickedFile.readAsBytes();
            debugPrint('Image read as bytes: ${bytes.length} bytes');

            // Check if image is too large (>5MB)
            if (bytes.length > 5 * 1024 * 1024) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Image is too large. Please select an image smaller than 5MB.'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
              return;
            }

            setState(() {
              _image = AppImage.fromBytes(
                bytes,
                name: pickedFile.name,
                mimeType: pickedFile.mimeType,
              );
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Image selected successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            debugPrint('Error reading image bytes: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error loading image: $e'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            }
          }
        } else {
          // For mobile platforms
          final file = File(pickedFile.path);
          final fileSize = await file.length();
          debugPrint('File size: $fileSize bytes');

          // Check if image is too large (>5MB)
          if (fileSize > 5 * 1024 * 1024) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Image is too large. Please select an image smaller than 5MB.'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
            return;
          }

          setState(() {
            _image = AppImage.fromFile(file);
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Image selected successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        debugPrint('No image selected');
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildImagePreview() {
    if (_image == null) return const SizedBox.shrink();

    if (kIsWeb && _image!.bytes != null) {
      // For web platform
      return Image.memory(
        _image!.bytes!,
        fit: BoxFit.cover,
      );
    } else if (!kIsWeb && _image!.file != null) {
      // For mobile platforms
      return Image.file(
        _image!.file!,
        fit: BoxFit.cover,
      );
    }

    return const Icon(
      Icons.image,
      size: 80,
      color: Colors.grey,
    );
  }

  Future<void> _saveProduct() async {
    if (_formKey.currentState?.validate() ?? false) {
      // Check if image is selected for new products
      if (!widget.isEditing && _image == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a product image'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        final productProvider = Provider.of<ProductProvider>(context, listen: false);

        // Create product model
        final product = ProductModel(
          id: widget.isEditing ? widget.product!.id : '',
          title: _titleController.text,
          description: _descriptionController.text,
          price: double.parse(_priceController.text),
          salePrice: _salePriceController.text.isNotEmpty
              ? double.parse(_salePriceController.text)
              : 0,
          totalStock: int.parse(_stockController.text),
          category: _selectedCategory,
          brand: _selectedBrand,
          image: widget.isEditing ? widget.product!.image : '',
          averageReview: widget.isEditing ? widget.product!.averageReview : 0,
        );

        debugPrint('Saving product: ${product.toJson()}');
        if (_image != null) {
          debugPrint('With image: ${_image!.isEmpty ? 'empty' : 'not empty'}');
        }

        bool success;
        if (widget.isEditing) {
          success = await productProvider.updateProduct(product, _image);
        } else {
          success = await productProvider.addProduct(product, _image);
        }

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.isEditing
                  ? 'Product updated successfully!'
                  : 'Product added successfully!'),
              backgroundColor: AppTheme.primaryColor,
            ),
          );
          Navigator.pop(context);
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(productProvider.errorMessage ??
                  'Failed to ${widget.isEditing ? 'update' : 'add'} product'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } catch (e) {
        debugPrint('Error saving product: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to ${widget.isEditing ? 'update' : 'add'} product: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isEditing ? 'Edit Product' : 'Add New Product'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              Center(
                child: GestureDetector(
                  onTap: _pickImage,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey),
                    ),
                    child: _image != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImagePreview(),
                          )
                        : _imageUrl != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  _imageUrl!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => const Icon(
                                    Icons.image,
                                    size: 80,
                                    color: Colors.grey,
                                  ),
                                ),
                              )
                            : const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_a_photo,
                                      size: 50,
                                      color: Colors.grey,
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Tap to add image',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Title Field
              CustomTextField(
                label: 'Product Title',
                hint: 'Enter product title',
                controller: _titleController,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description Field
              CustomTextField(
                label: 'Description',
                hint: 'Enter product description',
                controller: _descriptionController,
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Category Dropdown
              DropdownButtonFormField<String>(
                decoration: AppTheme.inputDecoration('Category'),
                value: _selectedCategory,
                items: _categories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a category';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Brand Dropdown
              DropdownButtonFormField<String>(
                decoration: AppTheme.inputDecoration('Brand'),
                value: _selectedBrand,
                items: _brands.map((brand) {
                  return DropdownMenuItem<String>(
                    value: brand,
                    child: Text(brand),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedBrand = value!;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a brand';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Price Fields
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      label: 'Price (₹)',
                      hint: 'Enter price',
                      controller: _priceController,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a price';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomTextField(
                      label: 'Sale Price (₹)',
                      hint: 'Enter sale price (optional)',
                      controller: _salePriceController,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'Please enter a valid number';
                          }
                          final salePrice = double.parse(value);
                          final price = double.parse(_priceController.text);
                          if (salePrice >= price) {
                            return 'Sale price must be less than regular price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Stock Field
              CustomTextField(
                label: 'Stock Quantity',
                hint: 'Enter stock quantity',
                controller: _stockController,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter stock quantity';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Save Button
              CustomButton(
                text: widget.isEditing ? 'Update Product' : 'Add Product',
                onPressed: _isLoading ? () {} : _saveProduct,
                isLoading: _isLoading,
                type: ButtonType.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
