import 'package:medicine_shop/core/widgets/animated_app_bar.dart';

/// A custom AppBar for screens that are part of the bottom navigation bar.
/// This AppBar doesn't show the back button.
///
/// This class now extends AnimatedAppBar to provide a consistent look
/// with the splash, onboarding, login, and register screens.
class BottomNavAppBar extends AnimatedAppBar {
  const BottomNavAppBar({
    super.key,
    required super.title,
    super.actions,
    super.bottom,
    super.centerTitle = false, // Left-align all titles
    super.elevation = 0,
    super.showAddressBar = false, // Only show address on home page
    super.onAddressPressed,
  }) : super(
          showBackButton: false,
          isSwiggyStyle: true, // Always use the simple style for bottom nav pages
        );
}
