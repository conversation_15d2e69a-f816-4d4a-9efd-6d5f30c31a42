import 'package:flutter/foundation.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';
import 'package:medicine_shop/features/profile/data/address_service.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';

enum AddressStatus {
  initial,
  loading,
  loaded,
  error,
}

class AddressProvider with ChangeNotifier {
  final AddressService _addressService;

  AddressStatus _status = AddressStatus.initial;
  List<AddressModel> _addresses = [];
  AddressModel? _selectedAddress;
  String? _errorMessage;

  AddressProvider({
    AddressService? addressService,
  }) : _addressService = addressService ?? AddressService() {
    // Initialize with a loading state to indicate addresses need to be loaded
    _status = AddressStatus.initial;
  }

  // Getters
  AddressStatus get status => _status;
  List<AddressModel> get addresses => _addresses;
  AddressModel? get selectedAddress => _selectedAddress;
  String? get errorMessage => _errorMessage;
  bool get hasAddresses => _addresses.isNotEmpty;

  // Load addresses
  Future<void> loadAddresses(UserModel user) async {
    _status = AddressStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final addresses = await _addressService.getAddresses(user.id);
      _addresses = addresses;

      // If there's a selected address and it's still in the list, keep it selected
      if (_selectedAddress != null) {
        final index = _addresses.indexWhere((address) => address.id == _selectedAddress!.id);
        if (index >= 0) {
          _selectedAddress = _addresses[index];
        } else {
          _selectedAddress = _addresses.isNotEmpty ? _addresses.first : null;
        }
      } else {
        _selectedAddress = _addresses.isNotEmpty ? _addresses.first : null;
      }

      _status = AddressStatus.loaded;
    } catch (e) {
      _status = AddressStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Add a new address
  Future<bool> addAddress(AddressModel address) async {
    _status = AddressStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final newAddress = await _addressService.addAddress(address);
      _addresses.add(newAddress);

      // If this is the first address, select it
      if (_addresses.length == 1) {
        _selectedAddress = newAddress;
      }

      _status = AddressStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = AddressStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update an address
  Future<bool> updateAddress(AddressModel address) async {
    _status = AddressStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedAddress = await _addressService.updateAddress(
        address.userId,
        address.id,
        address,
      );

      final index = _addresses.indexWhere((a) => a.id == address.id);
      if (index >= 0) {
        _addresses[index] = updatedAddress;
      }

      // If this was the selected address, update it
      if (_selectedAddress?.id == address.id) {
        _selectedAddress = updatedAddress;
      }

      _status = AddressStatus.loaded;
      notifyListeners();
      return true;
    } catch (e) {
      _status = AddressStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Delete an address
  Future<bool> deleteAddress(String userId, String addressId) async {
    _status = AddressStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _addressService.deleteAddress(userId, addressId);

      if (success) {
        _addresses.removeWhere((address) => address.id == addressId);

        // If this was the selected address, select another one
        if (_selectedAddress?.id == addressId) {
          _selectedAddress = _addresses.isNotEmpty ? _addresses.first : null;
        }
      }

      _status = AddressStatus.loaded;
      notifyListeners();
      return success;
    } catch (e) {
      _status = AddressStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Select an address
  void selectAddress(String addressId) {
    final index = _addresses.indexWhere((address) => address.id == addressId);
    if (index >= 0) {
      _selectedAddress = _addresses[index];
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
