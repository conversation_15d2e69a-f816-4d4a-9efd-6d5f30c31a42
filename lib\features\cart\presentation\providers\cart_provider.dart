import 'package:flutter/foundation.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';
import 'package:medicine_shop/features/cart/data/cart_service.dart';
import 'package:medicine_shop/features/cart/domain/models/cart_model.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';

enum CartStatus {
  initial,
  loading,
  loaded,
  error,
}

class CartProvider with ChangeNotifier {
  final CartService _cartService;

  CartStatus _status = CartStatus.initial;
  CartModel? _cart;
  String? _errorMessage;

  // This is the key change - we'll maintain a local list of items that the user has explicitly added
  final List<CartItemModel> _sessionItems = [];

  CartProvider({
    CartService? cartService,
  }) : _cartService = cartService ?? CartService();

  // Getters
  CartStatus get status => _status;
  CartModel? get cart => _cart;
  String? get errorMessage => _errorMessage;
  bool get isCartEmpty => _sessionItems.isEmpty;
  int get itemCount => _sessionItems.fold(0, (sum, item) => sum + item.quantity);
  double get subtotal => _sessionItems.fold(0, (sum, item) => sum + item.totalPrice);

  // Initialize cart - this is called when the app starts
  Future<void> initCart(UserModel? user) async {
    if (user == null) {
      _cart = CartModel(
        id: '',
        userId: '',
        items: [],
      );
      _status = CartStatus.loaded;
      notifyListeners();
      return;
    }

    // Create an empty cart with the user ID
    _cart = CartModel(
      id: '',
      userId: user.id,
      items: [],
    );
    _status = CartStatus.loaded;
    notifyListeners();
  }

  // Load cart items - we don't use this anymore, but keep it for compatibility
  Future<void> loadCart(UserModel user) async {
    // Instead of loading from the server, we just create an empty cart
    // and use our session items
    _status = CartStatus.loading;
    _errorMessage = null;
    notifyListeners();

    // Create a cart with the session items
    _cart = CartModel(
      id: '',
      userId: user.id,
      items: List.from(_sessionItems),
    );
    _status = CartStatus.loaded;
    notifyListeners();
  }

  // Add item to cart
  Future<void> addToCart(ProductModel product, int quantity) async {
    if (_cart == null) {
      _errorMessage = 'Cart not initialized. Please login first.';
      notifyListeners();
      return;
    }

    _status = CartStatus.loading;
    _errorMessage = null;

    // Check if this product is already in the session items
    final existingItemIndex = _sessionItems.indexWhere((item) => item.productId == product.id);

    if (existingItemIndex >= 0) {
      // Update the quantity if the item already exists
      _sessionItems[existingItemIndex].quantity += quantity;
    } else {
      // Add the new item if it doesn't exist
      final newItem = CartItemModel.fromProduct(product, quantity);
      _sessionItems.add(newItem);
    }

    // Update the cart with the session items
    _cart = CartModel(
      id: _cart!.id,
      userId: _cart!.userId,
      items: List.from(_sessionItems), // Create a copy of the session items
      createdAt: _cart!.createdAt,
      updatedAt: DateTime.now(),
    );

    _status = CartStatus.loaded;
    notifyListeners();

    // Sync with server in the background (but don't update the UI with the response)
    try {
      await _cartService.addToCart(
        _cart!.userId,
        product.id,
        quantity,
      );
    } catch (e) {
      // Just log the error, don't update the UI
      // This way, the user still sees their selected items
      _errorMessage = e.toString();
    }
  }

  // Update cart item quantity
  Future<void> updateCartItemQuantity(String productId, int quantity) async {
    if (_cart == null) {
      _errorMessage = 'Cart not initialized. Please login first.';
      notifyListeners();
      return;
    }

    // Update the session item
    final itemIndex = _sessionItems.indexWhere((item) => item.productId == productId);
    if (itemIndex >= 0) {
      _sessionItems[itemIndex].quantity = quantity;

      // Update the cart with the session items
      _cart = CartModel(
        id: _cart!.id,
        userId: _cart!.userId,
        items: List.from(_sessionItems),
        createdAt: _cart!.createdAt,
        updatedAt: DateTime.now(),
      );

      notifyListeners();
    }

    // Sync with server in the background
    try {
      await _cartService.updateCartItemQuantity(
        _cart!.userId,
        productId,
        quantity,
      );
    } catch (e) {
      // Just log the error, don't update the UI
      _errorMessage = e.toString();
    }
  }

  // Remove item from cart
  Future<void> removeFromCart(String productId) async {
    if (_cart == null) {
      _errorMessage = 'Cart not initialized. Please login first.';
      notifyListeners();
      return;
    }

    // Remove the item from session items
    _sessionItems.removeWhere((item) => item.productId == productId);

    // Update the cart with the session items
    _cart = CartModel(
      id: _cart!.id,
      userId: _cart!.userId,
      items: List.from(_sessionItems),
      createdAt: _cart!.createdAt,
      updatedAt: DateTime.now(),
    );

    notifyListeners();

    // Sync with server in the background
    try {
      await _cartService.removeFromCart(
        _cart!.userId,
        productId,
      );
    } catch (e) {
      // Just log the error, don't update the UI
      _errorMessage = e.toString();
    }
  }

  // Clear cart
  void clearCart() {
    // Clear the session items
    _sessionItems.clear();

    if (_cart != null) {
      _cart = CartModel(
        id: _cart!.id,
        userId: _cart!.userId,
        items: [],
        createdAt: _cart!.createdAt,
        updatedAt: DateTime.now(),
      );
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
