import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/cart/presentation/providers/cart_provider.dart';
import 'package:medicine_shop/features/orders/presentation/providers/order_provider.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
// import 'package:fluttertoast/fluttertoast.dart'; // Not needed, but can be used for showing toast messages

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  String _selectedPaymentMethod = 'cod'; // Default to Cash on Delivery
  bool _isProcessing = false;
  late Razorpay _razorpay;

  @override
  void initState() {
    super.initState();
    // Initialize Razorpay
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

    // Load addresses when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final addressProvider = Provider.of<AddressProvider>(context, listen: false);

      if (authProvider.isAuthenticated && authProvider.user != null) {
        addressProvider.loadAddresses(authProvider.user!);
      }
    });
  }

  @override
  void dispose() {
    _razorpay.clear(); // Clear all event listeners
    super.dispose();
  }

  // Razorpay payment handlers
  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    // Payment was successful
    debugPrint('✅ Razorpay payment success: ${response.paymentId}, ${response.orderId}, ${response.signature}');

    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final cartProvider = Provider.of<CartProvider>(context, listen: false);

    // Show a temporary success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Payment successful! Processing your order...'),
        backgroundColor: AppTheme.primaryColor,
        duration: Duration(seconds: 2),
      ),
    );

    // Capture the payment with correct parameter names
    orderProvider.captureRazorpayPayment(
      orderId: orderProvider.pendingOrderId!,
      razorpayPaymentId: response.paymentId!,
      razorpayOrderId: response.orderId!,
      razorpaySignature: response.signature!,
    ).then((success) {
      if (mounted && success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order confirmed! Thank you for your purchase.'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );

        // Clear cart and navigate to orders screen
        cartProvider.clearCart();
        Navigator.pushReplacementNamed(context, '/orders');
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(orderProvider.errorMessage ?? 'Failed to confirm order'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    // Payment failed
    debugPrint('❌ Razorpay payment error: ${response.code}, ${response.message}');

    // Show a more user-friendly error message
    String errorMessage = 'Payment failed';

    // Handle specific error codes
    if (response.code == 2) {
      errorMessage = 'Payment cancelled by user';
    } else if (response.message != null && response.message!.isNotEmpty) {
      errorMessage = 'Payment failed: ${response.message}';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: AppTheme.errorColor,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Try Again',
          textColor: Colors.white,
          onPressed: () {
            // Get the order details again
            final orderProvider = Provider.of<OrderProvider>(context, listen: false);
            final authProvider = Provider.of<AuthProvider>(context, listen: false);

            // Re-open Razorpay checkout
            if (orderProvider.pendingOrderId != null &&
                orderProvider.razorpayOrderId != null &&
                orderProvider.razorpayAmount != null) {
              _openRazorpayCheckout(
                orderId: orderProvider.pendingOrderId!,
                razorpayOrderId: orderProvider.razorpayOrderId!,
                amount: orderProvider.razorpayAmount!,
                name: authProvider.user!.userName,
                contact: '', // You can add phone number field to user model if needed
                email: authProvider.user!.email,
              );
            }
          },
        ),
      ),
    );
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // External wallet was selected
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('External wallet selected: ${response.walletName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // Open Razorpay checkout
  void _openRazorpayCheckout({
    required String orderId,
    required String razorpayOrderId,
    required int amount,
    required String name,
    String? contact,
    String? email,
  }) {
    debugPrint('🔄 Opening Razorpay checkout for order: $orderId');
    debugPrint('Razorpay Order ID: $razorpayOrderId');
    debugPrint('Amount: $amount paise');

    // Create options for Razorpay checkout with improved details
    var options = {
      'key': 'rzp_test_OwZXl4RivBMDZR', // Your Razorpay key ID
      'amount': amount, // Amount is in paise
      'name': 'Medicine Shop',
      'description': 'Order #$orderId',
      'order_id': razorpayOrderId,
      'prefill': {
        'name': name,
        'contact': contact ?? '9999999999', // Default contact if not provided
        'email': email ?? '<EMAIL>', // Default email if not provided
      },
      'theme': {
        'color': '#4CAF50',
      },
      'modal': {
        'confirm_close': true, // Ask for confirmation when closing the payment modal
        'escape': true, // Allow closing the modal with ESC key
      },
      'notes': {
        'order_id': orderId, // Store the app's order ID in notes for reference
      },
      // For testing, enable all payment methods
      'config': {
        'display': {
          'blocks': {
            'banks': {
              'name': 'Pay via any Bank',
              'instruments': [
                { 'method': 'netbanking' },
                { 'method': 'card' },
                { 'method': 'upi' },
                { 'method': 'wallet' },
              ],
            },
          },
          'sequence': ['block.banks'],
          'preferences': {
            'show_default_blocks': false,
          },
        },
      },
      // Enable test mode for dummy payments
      'readonly': {
        'contact': true,
        'email': true,
      },
    };

    try {
      // Show a loading indicator before opening Razorpay
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening payment gateway...'),
          duration: Duration(seconds: 1),
          backgroundColor: AppTheme.primaryColor,
        ),
      );

      // Open Razorpay checkout
      _razorpay.open(options);
    } catch (e) {
      debugPrint('❌ Error opening Razorpay checkout: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening payment gateway: ${e.toString()}'),
          backgroundColor: AppTheme.errorColor,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  void _selectPaymentMethod(String method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  Future<void> _placeOrder() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final addressProvider = Provider.of<AddressProvider>(context, listen: false);
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);

    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to place an order'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    if (addressProvider.selectedAddress == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a delivery address'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    if (cartProvider.cart == null || cartProvider.cart!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Your cart is empty'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await orderProvider.createOrder(
        user: authProvider.user!,
        cart: cartProvider.cart!,
        address: addressProvider.selectedAddress!,
        paymentMethod: _selectedPaymentMethod,
      );

      if (success && mounted) {
        // If payment method is Razorpay, open the Razorpay checkout
        if (_selectedPaymentMethod == AppConstants.paymentMethodRazorpay) {
          // Store the order details for later use
          final orderId = orderProvider.pendingOrderId;
          final razorpayOrderId = orderProvider.razorpayOrderId;
          final amount = orderProvider.razorpayAmount;

          if (razorpayOrderId != null && amount != null) {
            // Open Razorpay checkout
            _openRazorpayCheckout(
              orderId: orderId!,
              razorpayOrderId: razorpayOrderId,
              amount: amount,
              name: authProvider.user!.userName,
              contact: '', // You can add phone number field to user model if needed
              email: authProvider.user!.email,
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to create Razorpay order'),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        } else {
          // For COD, show success message and navigate to orders screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Order placed successfully!'),
              backgroundColor: AppTheme.primaryColor,
            ),
          );

          // Clear cart and navigate to orders screen
          cartProvider.clearCart();
          Navigator.pushReplacementNamed(context, '/orders');
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(orderProvider.errorMessage ?? 'Failed to place order'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Checkout',
      ),
      body: _isProcessing
          ? const LoadingIndicator(message: 'Processing your order...')
          : _buildCheckoutContent(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildCheckoutContent() {
    return Consumer3<CartProvider, AddressProvider, AuthProvider>(
      builder: (context, cartProvider, addressProvider, authProvider, child) {
        if (!authProvider.isAuthenticated) {
          return const Center(
            child: Text('Please login to checkout'),
          );
        }

        if (cartProvider.isCartEmpty) {
          return const Center(
            child: Text('Your cart is empty'),
          );
        }

        if (addressProvider.status == AddressStatus.loading) {
          return const LoadingIndicator(message: 'Loading addresses...');
        }

        if (addressProvider.status == AddressStatus.error) {
          return CustomErrorWidget(
            message: addressProvider.errorMessage ?? 'Failed to load addresses',
            onRetry: () {
              if (authProvider.user != null) {
                addressProvider.loadAddresses(authProvider.user!);
              }
            },
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Delivery Address
              _buildAddressSection(addressProvider),
              const SizedBox(height: 24),

              // Order Summary
              _buildOrderSummary(cartProvider),
              const SizedBox(height: 24),

              // Payment Method
              _buildPaymentMethodSection(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddressSection(AddressProvider addressProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Delivery Address',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (addressProvider.hasAddresses) ...[
              // Display selected address
              if (addressProvider.selectedAddress != null)
                _buildAddressCard(addressProvider.selectedAddress!),

              const SizedBox(height: 16),

              // Change address button
              CustomButton(
                text: 'Change Address',
                onPressed: () {
                  // Show address selection dialog
                  _showAddressSelectionDialog(addressProvider);
                },
                type: ButtonType.secondary,
                isFullWidth: false,
              ),
            ] else ...[
              const Text('No addresses found. Please add a delivery address.'),
              const SizedBox(height: 16),
              CustomButton(
                text: 'Add New Address',
                onPressed: () {
                  // Navigate to add address screen
                  Navigator.pushNamed(context, '/profile/add-address');
                },
                type: ButtonType.primary,
                isFullWidth: false,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddressCard(AddressModel address) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.primaryColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            address.address,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text('${address.city}, ${address.pincode}'),
          const SizedBox(height: 4),
          Text('Phone: ${address.phone}'),
          if (address.notes.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text('Notes: ${address.notes}'),
          ],
        ],
      ),
    );
  }

  void _showAddressSelectionDialog(AddressProvider addressProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Delivery Address'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: addressProvider.addresses.length,
            itemBuilder: (context, index) {
              final address = addressProvider.addresses[index];
              final isSelected = addressProvider.selectedAddress?.id == address.id;

              return ListTile(
                title: Text(address.address),
                subtitle: Text('${address.city}, ${address.pincode}'),
                trailing: isSelected ? const Icon(Icons.check, color: AppTheme.primaryColor) : null,
                onTap: () {
                  addressProvider.selectAddress(address.id);
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Navigate to add address screen
              Navigator.pop(context);
              Navigator.pushNamed(context, '/profile/add-address');
            },
            child: const Text('Add New'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary(CartProvider cartProvider) {
    final cart = cartProvider.cart!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Summary',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Items count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Items (${cart.totalItems})'),
                Text('₹${cart.subtotal.toStringAsFixed(2)}'),
              ],
            ),
            const SizedBox(height: 8),

            // Shipping
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Shipping'),
                Text('FREE'),
              ],
            ),
            const SizedBox(height: 8),

            // Tax
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Tax'),
                Text('₹0.00'),
              ],
            ),

            const Divider(height: 24),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '₹${cart.subtotal.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Method',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Cash on Delivery
            RadioListTile<String>(
              title: const Text('Cash on Delivery'),
              subtitle: const Text('Pay when you receive the order'),
              value: 'cod',
              groupValue: _selectedPaymentMethod,
              onChanged: (value) => _selectPaymentMethod(value!),
              activeColor: AppTheme.primaryColor,
            ),

            // Razorpay
            RadioListTile<String>(
              title: const Text('Razorpay'),
              subtitle: const Text('Pay securely with Razorpay'),
              value: AppConstants.paymentMethodRazorpay,
              groupValue: _selectedPaymentMethod,
              onChanged: (value) => _selectPaymentMethod(value!),
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    final cartProvider = Provider.of<CartProvider>(context);

    if (cartProvider.isCartEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: CustomButton(
        text: 'Place Order',
        onPressed: _isProcessing ? () {} : _placeOrder,
        isLoading: _isProcessing,
        type: ButtonType.primary,
        icon: Icons.shopping_cart_checkout,
      ),
    );
  }
}
