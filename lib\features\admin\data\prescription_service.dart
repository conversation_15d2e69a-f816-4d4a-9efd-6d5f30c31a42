import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/admin/domain/models/prescription_model.dart';
import 'package:medicine_shop/features/profile/presentation/screens/user_prescription_upload_screen.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class PrescriptionService {
  final ApiService _apiService;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  PrescriptionService({
    ApiService? apiService,
  }) : _apiService = apiService ?? ApiService();

  // Upload a prescription image using the admin endpoint
  Future<String> uploadPrescriptionImage(File imageFile) async {
    try {
      print('Starting admin prescription image upload');

      // Use the Cloudinary upload endpoint
      final cloudinaryUri = Uri.parse('${AppConstants.baseUrl}/admin/products/upload-image');
      print('Cloudinary upload URL: $cloudinaryUri');

      // Create multipart request for Cloudinary
      final cloudinaryRequest = http.MultipartRequest('POST', cloudinaryUri);

      // Handle file upload differently for web and mobile
      if (kIsWeb) {
        // For web, we need to handle the file differently
        final multipartFile = http.MultipartFile.fromString(
          'my_file', // This matches the field name expected by the server
          'Web upload placeholder',
          filename: 'prescription_${DateTime.now().millisecondsSinceEpoch}.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        cloudinaryRequest.files.add(multipartFile);
        print('Added web file to Cloudinary request');
      } else {
        // For mobile platforms
        final fileStream = http.ByteStream(imageFile.openRead());
        final fileLength = await imageFile.length();
        print('File length: $fileLength bytes');

        final multipartFile = http.MultipartFile(
          'my_file', // This matches the field name expected by the server
          fileStream,
          fileLength,
          filename: 'prescription_${DateTime.now().millisecondsSinceEpoch}.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        cloudinaryRequest.files.add(multipartFile);
        print('Added mobile file to Cloudinary request');
      }

      // Add token to request headers
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      if (token != null) {
        cloudinaryRequest.headers['Authorization'] = 'Bearer $token';
        cloudinaryRequest.headers['Cookie'] = 'token=$token';
        print('Added token to Cloudinary request headers');
      }

      // Send the Cloudinary request
      print('Sending Cloudinary upload request...');
      final cloudinaryStreamedResponse = await cloudinaryRequest.send();
      final cloudinaryResponse = await http.Response.fromStream(cloudinaryStreamedResponse);
      print('Cloudinary response status: ${cloudinaryResponse.statusCode}');
      print('Cloudinary response body: ${cloudinaryResponse.body}');

      if (cloudinaryResponse.statusCode != 200) {
        throw Exception('Failed to upload image to Cloudinary: ${cloudinaryResponse.statusCode}');
      }

      final cloudinaryJsonResponse = json.decode(cloudinaryResponse.body);
      if (cloudinaryJsonResponse['success'] != true) {
        throw Exception('Cloudinary upload failed: ${cloudinaryJsonResponse['message']}');
      }

      // Get the Cloudinary URL from the response
      final cloudinaryUrl = cloudinaryJsonResponse['result']['url'];
      print('Successfully uploaded image to Cloudinary: $cloudinaryUrl');

      return cloudinaryUrl;
    } catch (e) {
      print('Error in uploadPrescriptionImage: $e');
      rethrow;
    }
  }

  // Add a new prescription
  Future<PrescriptionModel> addPrescription(PrescriptionModel prescription) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.adminPrescriptionsEndpoint}/create',
        data: prescription.toCreateJson(),
      );

      if (response['success'] == true) {
        return PrescriptionModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to add prescription');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get all prescriptions for admin
  Future<List<PrescriptionModel>> getAllPrescriptions() async {
    try {
      print('Fetching all prescriptions...');
      final response = await _apiService.get(
        AppConstants.adminPrescriptionsEndpoint,
      );

      if (response['success'] == true) {
        final List<dynamic> prescriptionsJson = response['data'];
        print('Received ${prescriptionsJson.length} prescriptions from server');

        // Debug: Print the raw JSON for each prescription
        for (var i = 0; i < prescriptionsJson.length; i++) {
          print('Prescription $i raw data:');
          print(prescriptionsJson[i]);
        }

        final prescriptions = prescriptionsJson.map((json) {
          try {
            return PrescriptionModel.fromJson(json);
          } catch (e) {
            print('Error parsing prescription: $e');
            print('Problematic JSON: $json');
            rethrow;
          }
        }).toList();

        print('Successfully parsed ${prescriptions.length} prescriptions');
        return prescriptions;
      } else {
        throw Exception(response['message'] ?? 'Failed to get prescriptions');
      }
    } catch (e) {
      print('Error in getAllPrescriptions: $e');
      rethrow;
    }
  }

  // Get prescriptions for a user
  Future<List<PrescriptionModel>> getUserPrescriptions(String userId) async {
    try {
      print('Fetching prescriptions for user: $userId');
      print('Using endpoint: ${AppConstants.prescriptionEndpoint}/user/$userId');

      // Use direct HTTP request instead of ApiService to better handle errors
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
        headers['Cookie'] = 'token=$token';
        print('🔑 Token added to request: GET ${AppConstants.prescriptionEndpoint}/user/$userId');
        print('🍪 Cookie: token=$token');
      }

      final uri = Uri.parse('${AppConstants.baseUrl}${AppConstants.prescriptionEndpoint}/user/$userId');
      final response = await http.get(uri, headers: headers);

      print('Response status: ${response.statusCode}');
      print('Response headers: ${response.headers}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        print('Response data: ${jsonResponse}');

        // Check if success is a boolean, string, or integer
        var success = jsonResponse['success'];
        if (success is int) {
          success = success == 1;
        } else if (success is String) {
          success = success.toLowerCase() == 'true';
        }

        if (success == true && jsonResponse['data'] != null) {
          final List<dynamic> prescriptionsJson = jsonResponse['data'];
          print('Found ${prescriptionsJson.length} prescriptions for user');
          return prescriptionsJson.map((json) => PrescriptionModel.fromJson(json)).toList();
        } else {
          print('No prescriptions found or success is false');
          return []; // Return empty list if no data
        }
      } else {
        print('Response status: ${response.statusCode}');
        print('Response data: ${response.body}');
        return []; // Return empty list on error
      }
    } catch (e) {
      print('Error fetching user prescriptions: $e');
      return []; // Return empty list instead of throwing to avoid UI errors
    }
  }

  // Update prescription status
  Future<PrescriptionModel> updatePrescriptionStatus(String prescriptionId, String status) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.adminPrescriptionsEndpoint}/status/$prescriptionId',
        data: {'status': status},
      );

      if (response['success'] == true) {
        return PrescriptionModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update prescription status');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Accept prescription and assign to admin
  Future<PrescriptionModel> acceptPrescription(String prescriptionId, String adminId) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.adminPrescriptionsEndpoint}/accept/$prescriptionId',
        data: {'adminId': adminId},
      );

      if (response['success'] == true) {
        return PrescriptionModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to accept prescription');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Delete a prescription
  Future<bool> deletePrescription(String prescriptionId) async {
    try {
      final response = await _apiService.delete(
        '${AppConstants.adminPrescriptionsEndpoint}/$prescriptionId',
      );

      return response['success'] == true;
    } catch (e) {
      rethrow;
    }
  }

  // Upload a prescription image using the direct upload endpoint
  Future<String> uploadPrescriptionImageDirect(File imageFile, {
    required String userId,
    required String userName,
    required String addressId,
    required String address,
    required String city,
    required String pincode,
    required String phone,
    String? notes,
  }) async {
    try {
      // Validate required address fields
      if (addressId.isEmpty || address.isEmpty || city.isEmpty || pincode.isEmpty || phone.isEmpty) {
        throw Exception('All address fields are required');
      }

      // Validate user information
      if (userId.isEmpty) {
        throw Exception('User ID is required');
      }

      if (userName.isEmpty) {
        throw Exception('User name is required');
      }

      print('Starting direct prescription upload');
      print('User ID: $userId, User Name: $userName');
      print('Address: $address, City: $city, Pincode: $pincode, Phone: $phone');

      // Create a direct upload request to match the website implementation
      // Use the direct upload URL from AppConstants (http://localhost:5000/upload)
      final uri = Uri.parse(AppConstants.directUploadUrl);
      print('Upload URL: $uri');

      // Create multipart request
      final request = http.MultipartRequest('POST', uri);

      // Add the prescription image file with the correct field name "prescription"
      // This is the key difference - the website uses "prescription" as the field name
      if (kIsWeb) {
        // For web platform
        print('Creating web file upload');

        // Check if we have image bytes from WebImageBytes
        if (WebImageBytes.imageBytes != null) {
          print('Using WebImageBytes: ${WebImageBytes.imageBytes!.length} bytes');

          // Create a multipart file from the bytes
          final multipartFile = http.MultipartFile.fromBytes(
            'prescription', // Match the field name used in the website
            WebImageBytes.imageBytes!,
            filename: WebImageBytes.filename ?? 'prescription_${DateTime.now().millisecondsSinceEpoch}.jpg',
            contentType: MediaType('image', 'jpeg'),
          );
          request.files.add(multipartFile);
        } else {
          // Fallback if no bytes are available
          print('No web image bytes available, using placeholder');
          final multipartFile = http.MultipartFile.fromString(
            'prescription', // Match the field name used in the website
            'Web upload placeholder',
            filename: 'prescription_${DateTime.now().millisecondsSinceEpoch}.jpg',
            contentType: MediaType('image', 'jpeg'),
          );
          request.files.add(multipartFile);
        }
      } else {
        // For mobile platforms
        print('Creating mobile file upload');
        final fileStream = http.ByteStream(imageFile.openRead());
        final fileLength = await imageFile.length();
        print('File length: $fileLength bytes');

        final multipartFile = http.MultipartFile(
          'prescription', // Match the field name used in the website
          fileStream,
          fileLength,
          filename: 'prescription_${DateTime.now().millisecondsSinceEpoch}.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        request.files.add(multipartFile);
      }

      // Add all the required fields exactly as the website does
      request.fields['userId'] = userId;
      request.fields['userName'] = userName;
      request.fields['addressId'] = addressId;
      request.fields['address'] = address;
      request.fields['city'] = city;
      request.fields['pincode'] = pincode;
      request.fields['phone'] = phone;
      if (notes != null && notes.isNotEmpty) {
        request.fields['notes'] = notes;
      }

      print('Request fields: ${request.fields}');
      print('Request files: ${request.files.map((f) => f.field).toList()}');

      // Add token to request headers if available
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
        request.headers['Cookie'] = 'token=$token';
      }

      // Send the request
      print('Sending prescription upload request...');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        print('Parsed JSON response: $jsonResponse');

        // Check if success is a boolean, string, or integer
        var success = jsonResponse['success'];
        if (success is int) {
          success = success == 1;
        } else if (success is String) {
          success = success.toLowerCase() == 'true';
        }

        if (success == true) {
          // Get the image URL from the response
          final data = jsonResponse['data'];
          final imageUrl = data['imageUrl'] ?? '';
          print('Successfully uploaded prescription with image: $imageUrl');
          return imageUrl;
        } else {
          final message = jsonResponse['message'] ?? 'Failed to upload prescription';
          print('Prescription upload failed: $message');
          throw Exception(message);
        }
      } else {
        // Print the error response for debugging
        print('Prescription upload error: ${response.body}');
        throw Exception('Failed to upload prescription: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in uploadPrescriptionImageDirect: $e');
      rethrow;
    }
  }
}
