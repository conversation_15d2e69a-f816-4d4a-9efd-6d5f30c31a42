import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/custom_error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/widgets/animated_app_bar.dart';
import 'package:medicine_shop/features/admin/domain/models/prescription_model.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/admin/presentation/widgets/prescription_status_badge.dart';

class PrescriptionListScreen extends StatefulWidget {
  const PrescriptionListScreen({super.key});

  @override
  State<PrescriptionListScreen> createState() => _PrescriptionListScreenState();
}

class _PrescriptionListScreenState extends State<PrescriptionListScreen> {
  @override
  void initState() {
    super.initState();
    // Load prescriptions when screen initializes
    Future.microtask(() {
      if (mounted) {
        Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
      }
    });
  }

  void _showStatusUpdateDialog(BuildContext context, PrescriptionModel prescription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Prescription Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current status: ${prescription.status}'),
            const SizedBox(height: 16),
            const Text('Select new status:'),
            const SizedBox(height: 8),
            _buildStatusButton(context, prescription, 'pending'),
            _buildStatusButton(context, prescription, 'processing'),
            _buildStatusButton(context, prescription, 'completed'),
            _buildStatusButton(context, prescription, 'rejected'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusButton(BuildContext context, PrescriptionModel prescription, String status) {
    final isCurrentStatus = prescription.status == status;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: CustomButton(
        text: status.toUpperCase(),
        onPressed: isCurrentStatus
            ? () {}
            : () {
                Navigator.pop(context);
                _updatePrescriptionStatus(prescription.id, status);
              },
        type: isCurrentStatus ? ButtonType.disabled : _getButtonTypeForStatus(status),
        isFullWidth: true,
      ),
    );
  }

  ButtonType _getButtonTypeForStatus(String status) {
    switch (status) {
      case 'pending':
        return ButtonType.warning;
      case 'processing':
        return ButtonType.info;
      case 'completed':
        return ButtonType.success;
      case 'rejected':
        return ButtonType.danger;
      default:
        return ButtonType.primary;
    }
  }

  Future<void> _updatePrescriptionStatus(String prescriptionId, String status) async {
    final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

    try {
      final success = await prescriptionProvider.updatePrescriptionStatus(prescriptionId, status);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Prescription status updated to $status'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Prescription Orders',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              Provider.of<PrescriptionProvider>(context, listen: false).loadAllPrescriptions();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshing prescriptions...'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<PrescriptionProvider>(
        builder: (context, prescriptionProvider, child) {
          if (prescriptionProvider.status == PrescriptionStatus.loading) {
            return const LoadingIndicator(message: 'Loading prescriptions...');
          }

          if (prescriptionProvider.status == PrescriptionStatus.error) {
            return CustomErrorWidget(
              message: prescriptionProvider.errorMessage ?? 'Failed to load prescriptions',
              onRetry: () => prescriptionProvider.loadAllPrescriptions(),
            );
          }

          final prescriptions = prescriptionProvider.prescriptions;

          if (prescriptions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.description_outlined, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text('No prescriptions found'),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Refresh',
                    onPressed: () => prescriptionProvider.loadAllPrescriptions(),
                    type: ButtonType.secondary,
                    icon: Icons.refresh,
                    isFullWidth: false,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: prescriptions.length,
            itemBuilder: (context, index) {
              final prescription = prescriptions[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          PrescriptionStatusBadge(status: prescription.status),
                          const Spacer(),
                          Text(
                            DateFormat('MMM d, yyyy').format(prescription.createdAt),
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Prescription #${prescription.id.substring(0, 8)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Customer: ${prescription.userName}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      if (prescription.notes.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Notes: ${prescription.notes}',
                          style: TextStyle(color: Colors.grey[800]),
                        ),
                      ],
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: CustomButton(
                              text: 'View Details',
                              onPressed: () {
                                // Navigate to prescription details
                                prescriptionProvider.selectPrescription(prescription.id);
                                Navigator.pushNamed(
                                  context,
                                  '/admin/prescription-details',
                                  arguments: prescription.id,
                                );
                              },
                              type: ButtonType.secondary,
                              icon: Icons.visibility,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomButton(
                              text: 'Update Status',
                              onPressed: () {
                                _showStatusUpdateDialog(context, prescription);
                              },
                              type: ButtonType.primary,
                              icon: Icons.update,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
