import 'dart:math';
import 'package:flutter/material.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class Animated<PERSON>uth<PERSON>ogo extends StatefulWidget {
  final IconData icon;
  final String title;
  final String subtitle;

  const AnimatedAuthLogo({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
  });

  @override
  State<AnimatedAuthLogo> createState() => _AnimatedAuthLogoState();
}

class _AnimatedAuthLogoState extends State<AnimatedAuthLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _rotationAnimation = Tween<double>(begin: -0.05, end: 0.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated logo container
            Transform.rotate(
              angle: _rotationAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Opacity(
                  opacity: _opacityAnimation.value,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          ScreenConstants.primaryGradientStart,
                          ScreenConstants.primaryGradientEnd,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: ScreenConstants.primaryGradientStart.withAlpha(40),
                          blurRadius: 15,
                          spreadRadius: 0,
                          offset: const Offset(0, 8),
                        ),
                        BoxShadow(
                          color: ScreenConstants.primaryGradientEnd.withAlpha(30),
                          blurRadius: 25,
                          spreadRadius: 0,
                          offset: const Offset(0, 12),
                        ),
                      ],
                    ),
                    child: _buildIconWithEffects(),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Title
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: ScreenConstants.titleColor,
                letterSpacing: 0.5,
                shadows: [
                  Shadow(
                    color: ScreenConstants.primaryGradientStart.withAlpha(40),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            
            // Subtitle
            Text(
              widget.subtitle,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ScreenConstants.descriptionColor,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }

  Widget _buildIconWithEffects() {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(25),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Icon(
            widget.icon,
            size: 60,
            color: Colors.white,
            semanticLabel: 'App logo',
          ),
        ),
      ),
    );
  }
}
