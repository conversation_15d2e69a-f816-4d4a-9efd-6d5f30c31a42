import 'package:flutter/material.dart';

class PageTransitionEffect extends StatelessWidget {
  final Widget child;
  final PageController pageController;
  final int index;
  final int itemCount;
  
  const PageTransitionEffect({
    Key? key,
    required this.child,
    required this.pageController,
    required this.index,
    required this.itemCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: pageController,
      builder: (context, child) {
        double value = 0;
        
        if (pageController.position.haveDimensions) {
          value = pageController.page! - index;
          
          // Scale the page down slightly as it moves away from center
          value = (1 - (value.abs() * 0.3)).clamp(0.0, 1.0);
        }
        
        return Transform.scale(
          scale: Curves.easeOutQuint.transform(value),
          child: child,
        );
      },
      child: AnimatedBuilder(
        animation: pageController,
        builder: (context, child) {
          double page = 0;
          double pageOffset = 0;
          double effect = 0;
          
          if (pageController.position.haveDimensions) {
            page = pageController.page ?? 0;
            pageOffset = page - index;
            effect = pageOffset.abs();
          }
          
          // Apply different effects based on direction
          if (pageOffset > 0) {
            // Moving left (next page)
            return Transform.translate(
              offset: Offset(effect * -100, 0),
              child: Opacity(
                opacity: 1.0 - effect.clamp(0.0, 0.8),
                child: child,
              ),
            );
          } else {
            // Moving right (previous page) or at rest
            return Transform.translate(
              offset: Offset(effect * 100, 0),
              child: Opacity(
                opacity: 1.0 - effect.clamp(0.0, 0.8),
                child: child,
              ),
            );
          }
        },
        child: child,
      ),
    );
  }
}
