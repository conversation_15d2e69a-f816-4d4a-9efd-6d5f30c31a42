class PrescriptionModel {
  final String id;
  final String userId;
  final String adminId;
  final String imageUrl;
  final String? cloudinaryId;
  final String? cloudinaryUrl;
  final String notes;
  final String status; // pending, processing, completed, rejected
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String userName; // Added userName property

  PrescriptionModel({
    required this.id,
    required this.userId,
    required this.adminId,
    required this.imageUrl,
    this.cloudinaryId,
    this.cloudinaryUrl,
    required this.notes,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.userName = 'Anonymous User', // Default value if not provided
  });

  factory PrescriptionModel.fromJson(Map<String, dynamic> json) {
    // Get user name from different possible sources
    String userName = 'Anonymous User';

    // Debug the JSON structure
    print('Processing prescription JSON: $json');

    // Try to extract userName from various possible locations
    if (json['userName'] != null && json['userName'].toString().isNotEmpty) {
      userName = json['userName'].toString();
      print('Found userName directly: $userName');
    } else if (json['userId'] != null) {
      if (json['userId'] is Map) {
        // If userId is populated as an object with name or userName field
        if (json['userId']['name'] != null && json['userId']['name'].toString().isNotEmpty) {
          userName = json['userId']['name'].toString();
          print('Found userName from userId.name: $userName');
        } else if (json['userId']['userName'] != null && json['userId']['userName'].toString().isNotEmpty) {
          userName = json['userId']['userName'].toString();
          print('Found userName from userId.userName: $userName');
        }
      }
    } else if (json['addressInfo'] != null && json['addressInfo'] is Map) {
      if (json['addressInfo']['userName'] != null && json['addressInfo']['userName'].toString().isNotEmpty) {
        userName = json['addressInfo']['userName'].toString();
        print('Found userName from addressInfo.userName: $userName');
      }
    }

    // If we still have Anonymous User, try one more approach with user field
    if (userName == 'Anonymous User' && json['user'] != null && json['user'] is Map) {
      if (json['user']['userName'] != null && json['user']['userName'].toString().isNotEmpty) {
        userName = json['user']['userName'].toString();
        print('Found userName from user.userName: $userName');
      } else if (json['user']['name'] != null && json['user']['name'].toString().isNotEmpty) {
        userName = json['user']['name'].toString();
        print('Found userName from user.name: $userName');
      }
    }

    print('Final userName: $userName');

    try {
      print('Parsing prescription JSON: $json');

      // Extract userId, handling both string and map cases
      String userId = '';
      if (json['userId'] != null) {
        if (json['userId'] is Map) {
          userId = json['userId']['_id']?.toString() ?? '';
        } else {
          userId = json['userId'].toString();
        }
      }

      // Extract adminId/assignedTo
      String adminId = '';
      if (json['assignedTo'] != null) {
        adminId = json['assignedTo'].toString();
      } else if (json['adminId'] != null) {
        adminId = json['adminId'].toString();
      }

      // Extract status, ensuring it's a string
      String status = 'pending';
      if (json['status'] != null) {
        status = json['status'].toString();
      }

      // Extract notes, ensuring it's a string
      String notes = '';
      // First check for top-level notes field
      if (json['notes'] != null) {
        notes = json['notes'].toString();
      }
      // Then check for notes in addressInfo (this is where it's actually stored in the server)
      else if (json['addressInfo'] != null && json['addressInfo'] is Map && json['addressInfo']['notes'] != null) {
        notes = json['addressInfo']['notes'].toString();
      }

      // Extract imageUrl, ensuring it's a string and complete URL
      String imageUrl = '';

      // First check if we have a Cloudinary URL
      if (json['cloudinaryUrl'] != null && json['cloudinaryUrl'].toString().isNotEmpty) {
        imageUrl = json['cloudinaryUrl'].toString();
        print('Using Cloudinary URL: $imageUrl');
      }
      // Then check for result.url from Cloudinary upload
      else if (json['result'] != null && json['result'] is Map && json['result']['url'] != null) {
        imageUrl = json['result']['url'].toString();
        print('Using Cloudinary result URL: $imageUrl');
      }
      // Then check for regular imageUrl
      else if (json['imageUrl'] != null) {
        imageUrl = json['imageUrl'].toString();

        // If it's already a Cloudinary URL, use it directly
        if (imageUrl.contains('cloudinary.com')) {
          print('Found Cloudinary URL in imageUrl: $imageUrl');
        }
        // If the imageUrl is a relative path, convert it to an absolute URL
        else if (imageUrl.isNotEmpty) {
          if (imageUrl.startsWith('/')) {
            // Handle paths with leading slash
            imageUrl = 'http://localhost:5000$imageUrl';
          } else if (!imageUrl.startsWith('http')) {
            // Handle paths without leading slash or http
            imageUrl = 'http://localhost:5000/$imageUrl';
          }

          print('Processed local image URL: $imageUrl');
        }
      }

      // Parse dates
      DateTime createdAt;
      if (json['uploadedAt'] != null) {
        createdAt = DateTime.parse(json['uploadedAt'].toString());
      } else if (json['createdAt'] != null) {
        createdAt = DateTime.parse(json['createdAt'].toString());
      } else {
        createdAt = DateTime.now();
      }

      DateTime? updatedAt;
      if (json['updatedAt'] != null) {
        try {
          updatedAt = DateTime.parse(json['updatedAt'].toString());
        } catch (e) {
          print('Error parsing updatedAt: $e');
          updatedAt = null;
        }
      }

      // Extract ID, ensuring it's a string
      String id = '';
      if (json['_id'] != null) {
        id = json['_id'].toString();
      } else if (json['id'] != null) {
        id = json['id'].toString();
      }

      return PrescriptionModel(
        id: id,
        userId: userId,
        adminId: adminId,
        imageUrl: imageUrl,
        cloudinaryId: json['cloudinaryId']?.toString(),
        cloudinaryUrl: json['cloudinaryUrl']?.toString(),
        notes: notes,
        status: status,
        userName: userName,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e) {
      print('Error parsing prescription: $e');
      print('Problematic JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'adminId': adminId,
      'imageUrl': imageUrl,
      'cloudinaryId': cloudinaryId,
      'cloudinaryUrl': cloudinaryUrl,
      'notes': notes,
      'status': status,
      'userName': userName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // For creating a new prescription
  Map<String, dynamic> toCreateJson() {
    // Make sure userName is included
    return {
      'userId': userId,
      'adminId': adminId,
      'imageUrl': imageUrl,
      'cloudinaryId': cloudinaryId,
      'cloudinaryUrl': cloudinaryUrl,
      'notes': notes,
      'status': status,
      'userName': userName, // Ensure userName is sent to the server
    };
  }

  // Create a copy with updated fields
  PrescriptionModel copyWith({
    String? id,
    String? userId,
    String? adminId,
    String? imageUrl,
    String? cloudinaryId,
    String? cloudinaryUrl,
    String? notes,
    String? status,
    String? userName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PrescriptionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      adminId: adminId ?? this.adminId,
      imageUrl: imageUrl ?? this.imageUrl,
      cloudinaryId: cloudinaryId ?? this.cloudinaryId,
      cloudinaryUrl: cloudinaryUrl ?? this.cloudinaryUrl,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      userName: userName ?? this.userName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isPending => status == 'pending';
  bool get isProcessing => status == 'processing';
  bool get isCompleted => status == 'completed';
  bool get isRejected => status == 'rejected';
}
