import 'package:flutter/material.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';

/// Constants specifically for splash and onboarding screens
/// Now references the centralized AppTheme colors
class ScreenConstants {
  // Enhanced Splash and Onboarding Screen Colors - Using centralized AppTheme
  static const Color primaryGradientStart = AppTheme.primaryGradientStart;
  static const Color primaryGradientEnd = Color.fromARGB(255, 61, 138, 232);
  static const Color accentColor = AppTheme.accentColor;

  // Background circle colors
  static const Color backgroundCirclePrimary = primaryGradientStart;
  static const Color backgroundCircleSecondary = primaryGradientEnd;

  // Text colors
  static const Color titleColor = AppTheme.textPrimaryColor;
  static const Color descriptionColor = AppTheme.textSecondaryColor;

  // Button colors
  static const Color buttonGradientStart = primaryGradientStart;
  static const Color buttonGradientEnd = primaryGradientEnd;

  // Container background colors
  static const Color containerBackgroundColor = AppTheme.highlightColor;

  // Particle colors
  static const Color particleColor = AppTheme.secondaryColor; // Purple

  // Icon container decoration with enhanced gradient and glow
  static BoxDecoration iconContainerDecoration = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        primaryGradientStart,
        primaryGradientEnd,
      ],
      stops: const [0.2, 0.9],
    ),
    borderRadius: BorderRadius.circular(30),
    boxShadow: [
      BoxShadow(
        color: primaryGradientStart.withAlpha(40),
        blurRadius: 20,
        spreadRadius: 1,
        offset: const Offset(0, 8),
      ),
      BoxShadow(
        color: primaryGradientEnd.withAlpha(30),
        blurRadius: 30,
        spreadRadius: 0,
        offset: const Offset(0, 12),
      ),
    ],
    border: Border.all(
      color: Colors.white.withAlpha(25), // 0.1 opacity
      width: 1.5,
    ),
  );

  // Button decoration with enhanced gradient and glow
  static BoxDecoration buttonDecoration = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        buttonGradientStart,
        buttonGradientEnd,
      ],
      stops: const [0.2, 0.9],
    ),
    borderRadius: BorderRadius.circular(30),
    boxShadow: [
      BoxShadow(
        color: buttonGradientStart.withAlpha(50),
        blurRadius: 12,
        spreadRadius: 0,
        offset: const Offset(0, 4),
      ),
      BoxShadow(
        color: buttonGradientEnd.withAlpha(30),
        blurRadius: 20,
        spreadRadius: 0,
        offset: const Offset(0, 8),
      ),
    ],
    border: Border.all(
      color: Colors.white.withAlpha(38), // 0.15 opacity
      width: 1,
    ),
  );

  // Description container decoration with enhanced glass effect
  static BoxDecoration descriptionContainerDecoration = BoxDecoration(
    color: containerBackgroundColor,
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withAlpha(5),
        blurRadius: 10,
        spreadRadius: 0,
        offset: const Offset(0, 2),
      ),
    ],
    border: Border.all(
      color: primaryGradientStart.withAlpha(25), // 0.1 opacity
      width: 1,
    ),
  );

  // Title text style
  static TextStyle titleTextStyle = TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.w800,
    color: titleColor,
    letterSpacing: 0.5,
    height: 1.1,
    shadows: [
      Shadow(
        color: primaryGradientStart.withAlpha(76), // 0.3 opacity
        offset: const Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );

  // Description text style
  static TextStyle descriptionTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: descriptionColor,
    height: 1.5,
  );

  // Button text style
  static const TextStyle buttonTextStyle = TextStyle(
    color: Colors.white,
    fontSize: 16,
    fontWeight: FontWeight.bold,
    letterSpacing: 0.5,
  );

  // Skip button text style
  static TextStyle skipButtonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: primaryGradientStart,
  );
}
