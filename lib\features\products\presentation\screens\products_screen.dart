import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/core/widgets/responsive_grid.dart';
import 'package:medicine_shop/features/cart/presentation/providers/cart_provider.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';
import 'package:medicine_shop/features/products/presentation/providers/product_provider.dart';
import 'package:medicine_shop/features/products/presentation/screens/product_details_screen.dart';
import 'package:medicine_shop/features/products/presentation/widgets/product_card.dart';
import 'package:medicine_shop/features/products/presentation/widgets/category_chips.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  bool _isFilterVisible = false;
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    // Load products when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _toggleFilterVisibility() {
    setState(() {
      _isFilterVisible = !_isFilterVisible;
    });
  }

  void _navigateToProductDetails(BuildContext context, ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(productId: product.id),
      ),
    );
  }

  void _addToCart(BuildContext context, ProductModel product) {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    cartProvider.addToCart(product, 1);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${product.title} added to cart'),
        backgroundColor: AppTheme.primaryColor,
        action: SnackBarAction(
          label: 'VIEW CART',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to cart screen
            Navigator.pushNamed(context, '/cart');
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            // App bar with curved corners, containing title, address and search box
            SliverAppBar(
              pinned: true,
              floating: false,
              snap: false,
              elevation: 0,
              backgroundColor: Colors.white,
              automaticallyImplyLeading: false, // Remove back arrow
              toolbarHeight: 120, // Increased height to accommodate top spacing
              expandedHeight: 120, // Same as toolbarHeight to avoid expansion
              titleSpacing: 0,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Add top padding to create space
                  SizedBox(height: 8),

                  // Title and address on left, icons on right - vertically centered
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Left side: Location, MediShop title, and address
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // MediShop with location icon - tappable
                            Padding(
                              padding: const EdgeInsets.only(left: 16.0),
                              child: GestureDetector(
                                onTap: () {
                                  // Navigate directly to the address tab using the dedicated route
                                  Navigator.pushNamed(context, '/profile/add-address');
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.location_on,
                                      color: Colors.blue,
                                      size: 22,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'MediShop',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black87,
                                        fontFamily: 'Poppins',
                                        letterSpacing: 0.2,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Icon(
                                      Icons.keyboard_arrow_down,
                                      color: Colors.blue,
                                      size: 18,
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // Address text - truncated to half length
                            Padding(
                              padding: const EdgeInsets.only(left: 16.0, top: 2),
                              child: Consumer<AddressProvider>(
                                builder: (context, addressProvider, child) {
                                  final address = addressProvider.selectedAddress;
                                  if (address != null) {
                                    // Truncate address to approximately half length
                                    final fullAddress = '${address.address}, ${address.city}';
                                    final truncatedAddress = fullAddress.length > 30
                                        ? '${fullAddress.substring(0, 30)}...'
                                        : fullAddress;

                                    return GestureDetector(
                                      onTap: () {
                                        // Navigate directly to the address tab using the dedicated route
                                        Navigator.pushNamed(context, '/profile/add-address');
                                      },
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            truncatedAddress,
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.grey[700],
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Poppins',
                                              letterSpacing: 0.1,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.edit_location_alt_outlined,
                                            size: 14,
                                            color: Colors.grey[700],
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return GestureDetector(
                                      onTap: () {
                                        // Navigate directly to the address tab using the dedicated route
                                        Navigator.pushNamed(context, '/profile/add-address');
                                      },
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            'Add delivery address',
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.blue,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Poppins',
                                              letterSpacing: 0.1,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.add_circle_outline,
                                            size: 14,
                                            color: Colors.blue,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Right side: Filter and cart icons - vertically centered
                      Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Filter button
                            GestureDetector(
                              onTap: _toggleFilterVisibility,
                              child: Icon(
                                Icons.tune,
                                color: Colors.blue,
                                size: 22,
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Cart button
                            Consumer<CartProvider>(
                              builder: (context, cartProvider, child) {
                                return Stack(
                                  clipBehavior: Clip.none,
                                  alignment: Alignment.center,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.pushNamed(context, '/cart');
                                      },
                                      child: Icon(
                                        Icons.shopping_cart_outlined,
                                        color: Colors.blue,
                                        size: 22,
                                      ),
                                    ),
                                    if (cartProvider.itemCount > 0)
                                      Positioned(
                                        right: -4,
                                        top: -4,
                                        child: Container(
                                          padding: const EdgeInsets.all(2),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          constraints: const BoxConstraints(
                                            minWidth: 14,
                                            minHeight: 14,
                                          ),
                                          child: Text(
                                            '${cartProvider.itemCount}',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 8,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Search box inside app bar
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
                    child: GestureDetector(
                      onTap: () {
                        showSearch(
                          context: context,
                          delegate: ProductSearchDelegate(
                            onProductSelected: (product) => _navigateToProductDetails(context, product),
                            onAddToCart: (product) => _addToCart(context, product),
                          ),
                        );
                      },
                      child: Container(
                        height: 44,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 12.0),
                              child: Icon(Icons.search, color: Colors.grey[600], size: 20),
                            ),
                            Expanded(
                              child: Text(
                                'Search for medicines, health products...',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Poppins',
                                  letterSpacing: 0.1,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 12.0),
                              child: Icon(Icons.mic, color: Colors.grey[600], size: 20),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              actions: [],
            ),
          ];
        },
        body: Container(
          color: Colors.white,
          child: Column(
            children: [
              // Filters
              if (_isFilterVisible)
                _buildFilters(),

              // Main Content
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () => Provider.of<ProductProvider>(context, listen: false).loadProducts(),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                    // Category Chips with simple clean design - centered
                    Container(
                      width: double.infinity,
                      color: Colors.white,
                      padding: const EdgeInsets.only(top: 4, bottom: 4),
                      child: CategoryChips(
                        categories: MedicineCategories.getCategories(),
                        selectedCategory: _selectedCategory,
                        onCategorySelected: (category) {
                          setState(() {
                            _selectedCategory = category;
                          });

                          // Apply category filter
                          if (category != null && category != 'All') {
                            Provider.of<ProductProvider>(context, listen: false)
                                .updateFilters(categories: [category]);
                          } else {
                            Provider.of<ProductProvider>(context, listen: false)
                                .updateFilters(categories: []);
                          }
                        },
                      ),
                    ),

                    // Section Title - Seamless design
                    Container(
                      color: Colors.grey.shade50,
                      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _selectedCategory == null || _selectedCategory == 'All'
                                ? 'All Products'
                                : _selectedCategory!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _isFilterVisible = !_isFilterVisible;
                              });
                            },
                            child: Row(
                              children: [
                                Text(
                                  'Filter',
                                  style: TextStyle(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.filter_list,
                                  size: 18,
                                  color: AppTheme.primaryColor,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Products Grid
                    _buildProductsGrid(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      ),
      ),
    );
  }

  Widget _buildFilters() {
    final productProvider = Provider.of<ProductProvider>(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey[100],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Categories
          const Text(
            'Categories',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: productProvider.availableCategories.map((category) {
              final isSelected = productProvider.selectedCategories.contains(category);
              return FilterChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (selected) {
                  final categories = List<String>.from(productProvider.selectedCategories);
                  if (selected) {
                    categories.add(category);
                  } else {
                    categories.remove(category);
                  }
                  productProvider.updateFilters(categories: categories);
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Brands
          const Text(
            'Brands',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: productProvider.availableBrands.map((brand) {
              final isSelected = productProvider.selectedBrands.contains(brand);
              return FilterChip(
                label: Text(brand),
                selected: isSelected,
                onSelected: (selected) {
                  final brands = List<String>.from(productProvider.selectedBrands);
                  if (selected) {
                    brands.add(brand);
                  } else {
                    brands.remove(brand);
                  }
                  productProvider.updateFilters(brands: brands);
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Sort By
          const Text(
            'Sort By',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: productProvider.sortBy,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            items: const [
              DropdownMenuItem(
                value: 'price-lowtohigh',
                child: Text('Price: Low to High'),
              ),
              DropdownMenuItem(
                value: 'price-hightolow',
                child: Text('Price: High to Low'),
              ),
              DropdownMenuItem(
                value: 'title-atoz',
                child: Text('Name: A to Z'),
              ),
              DropdownMenuItem(
                value: 'title-ztoa',
                child: Text('Name: Z to A'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                productProvider.updateFilters(sortBy: value);
              }
            },
          ),

          const SizedBox(height: 16),

          // Clear Filters Button
          CustomButton(
            text: 'Clear Filters',
            onPressed: () {
              productProvider.clearFilters();
            },
            type: ButtonType.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildProductsGrid() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.status == ProductLoadingStatus.loading) {
          return SizedBox(
            height: 300,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (productProvider.status == ProductLoadingStatus.error) {
          return SizedBox(
            height: 300,
            child: CustomErrorWidget(
              message: productProvider.errorMessage ?? 'Failed to load products',
              onRetry: () => productProvider.loadProducts(),
            ),
          );
        }

        final products = productProvider.filteredProducts;

        if (products.isEmpty) {
          return SizedBox(
            height: 300,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'No products found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Try changing your search or filter criteria',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ResponsiveGrid(
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 0.65, // Adjusted for new card design
            children: products.map((product) {
              return ProductCard(
                product: product,
                onTap: () => _navigateToProductDetails(context, product),
                onAddToCart: () => _addToCart(context, product),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

class ProductSearchDelegate extends SearchDelegate<ProductModel?> {
  final Function(ProductModel) onProductSelected;
  final Function(ProductModel) onAddToCart;

  ProductSearchDelegate({
    required this.onProductSelected,
    required this.onAddToCart,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.mic),
        onPressed: () {
          // Voice search functionality would be implemented here
          // For now, just show a snackbar
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Voice search coming soon!'),
              duration: Duration(seconds: 2),
            ),
          );
        },
      ),
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.trim().isEmpty) {
      return const Center(
        child: Text('Please enter a search term'),
      );
    }

    final productProvider = Provider.of<ProductProvider>(context, listen: false);
    productProvider.searchProducts(query);

    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.status == ProductLoadingStatus.loading) {
          return const LoadingIndicator(message: 'Searching...');
        }

        if (productProvider.status == ProductLoadingStatus.error) {
          return CustomErrorWidget(
            message: productProvider.errorMessage ?? 'Search failed',
            onRetry: () => productProvider.searchProducts(query),
          );
        }

        final products = productProvider.filteredProducts;

        if (products.isEmpty) {
          return const Center(
            child: Text('No products found'),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: ResponsiveGrid(
            physics: const AlwaysScrollableScrollPhysics(),
            childAspectRatio: 0.7, // Increased to prevent overflow
            children: products.map((product) {
              return ProductCard(
                product: product,
                onTap: () {
                  onProductSelected(product);
                  close(context, product);
                },
                onAddToCart: () => onAddToCart(product),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.trim().isEmpty) {
      return const Center(
        child: Text('Search for medicines, vitamins, first aid products by name, category, or brand'),
      );
    }

    final productProvider = Provider.of<ProductProvider>(context, listen: false);
    productProvider.searchProducts(query);

    return buildResults(context);
  }
}
