import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/onboarding/presentation/screens/onboarding_screen.dart';
import 'package:flutter/services.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Set system UI overlay style for a more immersive experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    // Initialize animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutBack),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.1, 0.6, curve: Curves.easeOut),
      ),
    );

    // Start animation
    _animationController.forward();

    // Navigate to next screen after delay
    _checkAuthAndNavigate();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkAuthAndNavigate() async {
    // Ensure the splash screen shows for at least 3 seconds
    await Future.delayed(const Duration(milliseconds: 3000));

    if (!mounted) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Wait for authentication check to complete
    // This ensures we have the correct authentication state
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    // Check if user is already logged in
    final isLoggedIn = authProvider.isAuthenticated;

    // Navigate to appropriate screen
    if (isLoggedIn) {
      print("User is logged in, navigating to home");
      // User is logged in, go directly to main app with bottom navigation
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      print("User is not logged in, navigating to onboarding");
      // User is not logged in, show onboarding
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const OnboardingScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(position: offsetAnimation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Background design elements - matching onboarding screen
          Positioned(
            top: -size.height * 0.15,
            right: -size.width * 0.4,
            child: Container(
              width: size.width * 0.8,
              height: size.width * 0.8,
              decoration: BoxDecoration(
                color: ScreenConstants.backgroundCirclePrimary.withAlpha(30),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: -size.height * 0.1,
            left: -size.width * 0.3,
            child: Container(
              width: size.width * 0.7,
              height: size.width * 0.7,
              decoration: BoxDecoration(
                color: ScreenConstants.backgroundCircleSecondary.withAlpha(20),
                shape: BoxShape.circle,
              ),
            ),
          ),

          // Add particles for visual consistency with onboarding
          Positioned.fill(
            child: CustomPaint(
              painter: ParticlesPainter(
                color: ScreenConstants.particleColor,
                particleCount: 20,
              ),
            ),
          ),

          // Main content
          SafeArea(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo animation
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: SlideTransition(
                              position: _slideAnimation,
                              child: child,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: ScreenConstants.iconContainerDecoration,
                        child: const Center(
                          child: Icon(
                            Icons.medical_services_outlined,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // App name with animation
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: Column(
                        children: [
                          Text(
                            'MediCare',
                            style: ScreenConstants.titleTextStyle.copyWith(
                              fontSize: 42,
                              letterSpacing: 1.5,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: ScreenConstants.descriptionContainerDecoration,
                            child: Text(
                              'Your Health, Our Priority',
                              style: ScreenConstants.descriptionTextStyle.copyWith(
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 60),

                    // Loading indicator
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: child,
                        );
                      },
                      child: SizedBox(
                        width: 50,
                        height: 50,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(ScreenConstants.primaryGradientStart),
                          strokeWidth: 3,
                          backgroundColor: ScreenConstants.primaryGradientStart.withAlpha(30),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Enhanced custom painter for particles with glow effects
class ParticlesPainter extends CustomPainter {
  final Color color;
  final int particleCount;
  final List<Particle> particles = [];

  ParticlesPainter({required this.color, required this.particleCount}) {
    // Initialize particles with random positions and sizes
    for (int i = 0; i < particleCount; i++) {
      particles.add(Particle.random());
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Draw particles with enhanced visual effects
    for (var particle in particles) {
      final position = Offset(
        particle.x * size.width,
        particle.y * size.height,
      );

      // Base particle
      final particlePaint = Paint()
        ..color = color.withAlpha((particle.alpha * 255).toInt())
        ..style = PaintingStyle.fill;

      // Draw the particle
      final particleSize = particle.size * (particle.isGlowing ? 4 : 3);
      canvas.drawCircle(
        position,
        particleSize,
        particlePaint,
      );

      // Add glow effect for special particles
      if (particle.isGlowing) {
        // Outer glow
        final glowPaint = Paint()
          ..color = color.withAlpha((particle.alpha * 0.5 * 255).toInt())
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          position,
          particleSize * 1.8,
          glowPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Enhanced Particle class for the animated background
class Particle {
  final double x;
  final double y;
  final double size;
  final double alpha;
  final bool isGlowing;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.alpha,
    required this.isGlowing,
  });

  // Create a random particle with improved randomization
  factory Particle.random() {
    // Create some particles that glow (about 30%)
    final bool isGlowing = _randomDouble(0, 1) < 0.3;

    return Particle(
      x: _randomDouble(0, 1),
      y: _randomDouble(0, 1),
      // Vary the size more for visual interest
      size: _randomDouble(0.005, isGlowing ? 0.04 : 0.025),
      // Glowing particles are more visible
      alpha: _randomDouble(isGlowing ? 0.4 : 0.1, isGlowing ? 0.8 : 0.5),
      isGlowing: isGlowing,
    );
  }

  // Helper method to generate random doubles with improved randomization
  static double _randomDouble(double minValue, double maxValue) {
    // Using microseconds for better randomization
    final random = (DateTime.now().microsecondsSinceEpoch % 10000) / 10000;
    return minValue + (maxValue - minValue) * random;
  }
}