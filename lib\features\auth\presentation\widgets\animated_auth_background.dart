import 'package:flutter/material.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class Animated<PERSON>uthBackground extends StatelessWidget {
  final Widget child;

  const AnimatedAuthBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Stack(
      children: [
        // Simple gradient background
        Container(
          width: size.width,
          height: size.height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                ScreenConstants.containerBackgroundColor, // Light blue tint
                Colors.white,
              ],
            ),
          ),
        ),

        // Top right decorative circle - subtle
        Positioned(
          top: -size.width * 0.1,
          right: -size.width * 0.1,
          child: Container(
            width: size.width * 0.5,
            height: size.width * 0.5,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  ScreenConstants.primaryGradientStart.withAlpha(15), // Very subtle
                  Colors.transparent,
                ],
                stops: const [0.5, 1.0],
              ),
              shape: BoxShape.circle,
            ),
          ),
        ),

        // Bottom left decorative circle - subtle
        Positioned(
          bottom: -size.width * 0.1,
          left: -size.width * 0.1,
          child: Container(
            width: size.width * 0.4,
            height: size.width * 0.4,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  ScreenConstants.primaryGradientEnd.withAlpha(15), // Very subtle
                  Colors.transparent,
                ],
                stops: const [0.5, 1.0],
              ),
              shape: BoxShape.circle,
            ),
          ),
        ),

        // Main content
        child,
      ],
    );
  }
}
