import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class PromoCarousel extends StatefulWidget {
  final List<PromoItem> items;
  final double height;
  final bool autoPlay;
  final Duration autoPlayInterval;
  final Duration autoPlayAnimationDuration;

  const PromoCarousel({
    super.key,
    required this.items,
    this.height = 180,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 5),
    this.autoPlayAnimationDuration = const Duration(milliseconds: 800),
  });

  @override
  State<PromoCarousel> createState() => _PromoCarouselState();
}

class _PromoCarouselState extends State<PromoCarousel> {
  int _currentIndex = 0;
  final carousel.CarouselController _carouselController = carousel.CarouselController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        carousel.CarouselSlider(
          carouselController: _carouselController,
          options: carousel.CarouselOptions(
            height: widget.height,
            viewportFraction: 0.92,
            enlargeCenterPage: true,
            enableInfiniteScroll: widget.items.length > 1,
            autoPlay: widget.autoPlay && widget.items.length > 1,
            autoPlayInterval: widget.autoPlayInterval,
            autoPlayAnimationDuration: widget.autoPlayAnimationDuration,
            onPageChanged: (index, reason) {
              setState(() {
                _currentIndex = index;
              });
            },
          ),
          items: widget.items.map((item) {
            return Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: item.onTap,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    margin: const EdgeInsets.symmetric(horizontal: 5.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(20),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Stack(
                        children: [
                          // Background Image or Placeholder
                          Positioned.fill(
                            child: Builder(
                              builder: (context) {
                                try {
                                  return Image.asset(
                                    item.imageAsset,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      // Use placeholder color if image not found
                                      return Container(
                                        color: item.placeholderColor ?? AppTheme.primaryColor,
                                      );
                                    },
                                  );
                                } catch (e) {
                                  // Fallback for any other errors
                                  return Container(
                                    color: item.placeholderColor ?? AppTheme.primaryColor,
                                  );
                                }
                              },
                            ),
                          ),

                          // Gradient Overlay
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withAlpha(150),
                                  ],
                                  stops: const [0.6, 1.0],
                                ),
                              ),
                            ),
                          ),

                          // Content
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.title,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          blurRadius: 2,
                                          color: Colors.black,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    item.subtitle,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      shadows: [
                                        Shadow(
                                          blurRadius: 2,
                                          color: Colors.black,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        if (widget.items.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: AnimatedSmoothIndicator(
              activeIndex: _currentIndex,
              count: widget.items.length,
              effect: ExpandingDotsEffect(
                dotHeight: 8,
                dotWidth: 8,
                activeDotColor: AppTheme.primaryColor,
                dotColor: Colors.grey.shade300,
                spacing: 6,
              ),
            ),
          ),
      ],
    );
  }
}

class PromoItem {
  final String imageAsset;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Color? placeholderColor;

  PromoItem({
    required this.imageAsset,
    required this.title,
    required this.subtitle,
    this.onTap,
    this.placeholderColor,
  });
}
