import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';

class AddressService {
  final ApiService _apiService;

  AddressService({
    ApiService? apiService,
  }) : _apiService = apiService ?? ApiService();

  // Add a new address
  Future<AddressModel> addAddress(AddressModel address) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.shopAddressEndpoint}/add',
        data: address.toCreateJson(),
      );

      if (response['success'] == true) {
        return AddressModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to add address');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get all addresses for a user
  Future<List<AddressModel>> getAddresses(String userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.shopAddressEndpoint}/get/$userId',
      );

      if (response['success'] == true) {
        final List<dynamic> addressesJson = response['data'];
        return addressesJson.map((json) => AddressModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get addresses');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Update an address
  Future<AddressModel> updateAddress(String userId, String addressId, AddressModel address) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.shopAddressEndpoint}/update/$userId/$addressId',
        data: address.toUpdateJson(),
      );

      if (response['success'] == true) {
        return AddressModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update address');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Delete an address
  Future<bool> deleteAddress(String userId, String addressId) async {
    try {
      final response = await _apiService.delete(
        '${AppConstants.shopAddressEndpoint}/delete/$userId/$addressId',
      );

      return response['success'] == true;
    } catch (e) {
      rethrow;
    }
  }
}
