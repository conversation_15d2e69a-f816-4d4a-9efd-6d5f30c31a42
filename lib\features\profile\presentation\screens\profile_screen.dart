import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/widgets/bottom_nav_app_bar.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/custom_text_field.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';
import 'package:medicine_shop/features/profile/presentation/screens/user_order_history_screen.dart';
import 'package:medicine_shop/features/profile/presentation/screens/user_prescription_history_screen.dart';

class ProfileScreen extends StatefulWidget {
  final int initialTab;

  const ProfileScreen({super.key, this.initialTab = 0});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Profile form
  final _profileFormKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _profilePhoneController = TextEditingController();

  // Address form
  final _addressFormKey = GlobalKey<FormState>();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _addressPhoneController = TextEditingController();
  final _addressNotesController = TextEditingController();

  bool _isLoading = false;
  bool _isAddingAddress = false;
  bool _isEditingAddress = false;
  String? _editingAddressId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this, initialIndex: widget.initialTab);

    // Load user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final addressProvider = Provider.of<AddressProvider>(context, listen: false);

      if (authProvider.isAuthenticated && authProvider.user != null) {
        _nameController.text = authProvider.user!.userName;
        // Set email (not nullable according to UserModel)
        _emailController.text = authProvider.user!.email;

        // Set phone number if available
        if (authProvider.user!.phoneNumber != null) {
          _profilePhoneController.text = authProvider.user!.phoneNumber!;
        }

        // Load addresses
        addressProvider.loadAddresses(authProvider.user!);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _profilePhoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _pincodeController.dispose();
    _addressPhoneController.dispose();
    _addressNotesController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (_profileFormKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Here you would update the user profile in the backend
        // For now, we'll just show a success message
        await Future.delayed(const Duration(seconds: 1));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: AppTheme.primaryColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update profile: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _addAddress() async {
    if (_addressFormKey.currentState?.validate() ?? false) {
      setState(() {
        _isAddingAddress = true;
      });

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final addressProvider = Provider.of<AddressProvider>(context, listen: false);

        if (authProvider.user != null) {
          if (_isEditingAddress && _editingAddressId != null) {
            // Update existing address
            final updatedAddress = AddressModel(
              id: _editingAddressId!,
              userId: authProvider.user!.id,
              address: _addressController.text,
              city: _cityController.text,
              pincode: _pincodeController.text,
              phone: _addressPhoneController.text,
              notes: _addressNotesController.text,
            );

            final success = await addressProvider.updateAddress(updatedAddress);

            if (success && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Address updated successfully!'),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );

              // Clear form fields and reset editing state
              _resetAddressForm();
            } else if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(addressProvider.errorMessage ?? 'Failed to update address'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            }
          } else {
            // Add new address
            final newAddress = AddressModel(
              id: '', // Will be assigned by the server
              userId: authProvider.user!.id,
              address: _addressController.text,
              city: _cityController.text,
              pincode: _pincodeController.text,
              phone: _addressPhoneController.text,
              notes: _addressNotesController.text,
            );

            final success = await addressProvider.addAddress(newAddress);

            if (success && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Address added successfully!'),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );

              // Clear form fields
              _resetAddressForm();
            } else if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(addressProvider.errorMessage ?? 'Failed to add address'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            }
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isAddingAddress = false;
          });
        }
      }
    }
  }

  void _resetAddressForm() {
    setState(() {
      _isEditingAddress = false;
      _editingAddressId = null;
      _addressController.clear();
      _cityController.clear();
      _pincodeController.clear();
      _addressPhoneController.clear();
      _addressNotesController.clear();
    });
  }

  void _editAddress(AddressModel address) {
    setState(() {
      _isEditingAddress = true;
      _editingAddressId = address.id;
      _addressController.text = address.address;
      _cityController.text = address.city;
      _pincodeController.text = address.pincode;
      _addressPhoneController.text = address.phone;
      _addressNotesController.text = address.notes;
    });

    // Scroll to the form
    Future.delayed(const Duration(milliseconds: 300), () {
      Scrollable.ensureVisible(
        _addressFormKey.currentContext!,
        duration: const Duration(milliseconds: 500),
        alignment: 0.5,
      );
    });
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // First logout
              Provider.of<AuthProvider>(context, listen: false).logout();
              // Then navigate to login screen
              Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BottomNavAppBar(
        title: 'My Profile',
        // Title is left-aligned by default
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logout',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Profile'),
            Tab(text: 'Addresses'),
            Tab(text: 'Orders'),
            Tab(text: 'Prescriptions'),
          ],
          labelColor: Colors.black87,
          unselectedLabelColor: Colors.grey,
          indicatorColor: ScreenConstants.primaryGradientStart,
          indicatorWeight: 3,
          isScrollable: true,
        ),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.status == AuthStatus.authenticating) {
            return const LoadingIndicator(message: 'Loading profile...');
          }

          if (!authProvider.isAuthenticated) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Please login to view your profile'),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Login',
                    onPressed: () {
                      Navigator.pushNamed(context, '/auth');
                    },
                    isFullWidth: false,
                    type: ButtonType.primary,
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildProfileTab(),
              _buildAddressesTab(),
              _buildOrdersTab(),
              _buildPrescriptionsTab(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _profileFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Personal Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Profile Picture
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey[200],
                    child: const Icon(
                      Icons.person,
                      size: 50,
                      color: Colors.grey,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.camera_alt, color: Colors.white, size: 20),
                        onPressed: () {
                          // Implement image picker
                        },
                        constraints: const BoxConstraints(
                          minWidth: 40,
                          minHeight: 40,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Name Field
            CustomTextField(
              label: 'Full Name',
              hint: 'Enter your full name',
              controller: _nameController,
              prefixIcon: Icons.person,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email Field (Optional)
            CustomTextField(
              label: 'Email (Optional)',
              hint: 'Enter your email',
              controller: _emailController,
              prefixIcon: Icons.email,
              readOnly: true, // Email cannot be changed
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Phone Field
            CustomTextField(
              label: 'Phone Number',
              hint: 'Enter your phone number',
              controller: _profilePhoneController,
              prefixIcon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Save Button
            CustomButton(
              text: 'Save Profile',
              onPressed: _isLoading ? () {} : _saveProfile,
              isLoading: _isLoading,
              type: ButtonType.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressesTab() {
    return Consumer2<AuthProvider, AddressProvider>(
      builder: (context, authProvider, addressProvider, child) {
        if (addressProvider.status == AddressStatus.loading) {
          return const LoadingIndicator(message: 'Loading addresses...');
        }

        if (addressProvider.status == AddressStatus.error) {
          return CustomErrorWidget(
            message: addressProvider.errorMessage ?? 'Failed to load addresses',
            onRetry: () {
              if (authProvider.user != null) {
                addressProvider.loadAddresses(authProvider.user!);
              }
            },
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Existing Addresses
              if (addressProvider.hasAddresses) ...[
                const Text(
                  'Your Addresses',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: addressProvider.addresses.length,
                  itemBuilder: (context, index) {
                    final address = addressProvider.addresses[index];
                    final isSelected = addressProvider.selectedAddress?.id == address.id;
                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: isSelected
                            ? const BorderSide(color: AppTheme.primaryColor, width: 2)
                            : BorderSide.none,
                      ),
                      child: InkWell(
                        onTap: () => addressProvider.selectAddress(address.id),
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      address.address,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                  ),
                                ),
                                if (isSelected)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryColor.withAlpha(30),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(Icons.check_circle, color: AppTheme.primaryColor, size: 16),
                                        SizedBox(width: 4),
                                        Text(
                                          'Selected',
                                          style: TextStyle(
                                            color: AppTheme.primaryColor,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text('${address.city}, ${address.pincode}'),
                            const SizedBox(height: 4),
                            Text('Phone: ${address.phone}'),
                            if (address.notes.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text('Notes: ${address.notes}'),
                            ],
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                TextButton.icon(
                                  icon: const Icon(Icons.edit),
                                  label: const Text('Edit'),
                                  onPressed: () => _editAddress(address),
                                ),
                                TextButton.icon(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  label: const Text('Delete', style: TextStyle(color: Colors.red)),
                                  onPressed: () {
                                    // Implement delete address
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Delete Address'),
                                        content: const Text('Are you sure you want to delete this address?'),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.pop(context),
                                            child: const Text('Cancel'),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              final userId = authProvider.user!.id;
                                              final addressId = address.id;
                                              final isEditingThisAddress = _editingAddressId == addressId;
                                              Navigator.pop(context);

                                              // Store context reference before async gap
                                              final scaffoldMessenger = ScaffoldMessenger.of(context);

                                              addressProvider.deleteAddress(userId, addressId).then((success) {
                                                if (success && mounted) {
                                                  scaffoldMessenger.showSnackBar(
                                                    const SnackBar(
                                                      content: Text('Address deleted successfully!'),
                                                      backgroundColor: AppTheme.primaryColor,
                                                    ),
                                                  );

                                                  // If we were editing this address, reset the form
                                                  if (isEditingThisAddress) {
                                                    _resetAddressForm();
                                                  }
                                                }
                                              });
                                            },
                                            child: const Text('Delete'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    );
                  },
                ),

                const Divider(height: 32),
              ],

              // Add New Address Form
              const Text(
                'Add New Address',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              Form(
                key: _addressFormKey,
                child: Column(
                  children: [
                    CustomTextField(
                      label: 'Address',
                      hint: 'Enter your address',
                      controller: _addressController,
                      prefixIcon: Icons.home,
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your address';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CustomTextField(
                      label: 'City',
                      hint: 'Enter your city',
                      controller: _cityController,
                      prefixIcon: Icons.location_city,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your city';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CustomTextField(
                      label: 'Pincode',
                      hint: 'Enter your pincode',
                      controller: _pincodeController,
                      prefixIcon: Icons.pin_drop,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your pincode';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CustomTextField(
                      label: 'Phone Number',
                      hint: 'Enter your phone number',
                      controller: _addressPhoneController,
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CustomTextField(
                      label: 'Notes (Optional)',
                      hint: 'Any special instructions for delivery',
                      controller: _addressNotesController,
                      prefixIcon: Icons.note,
                      maxLines: 2,
                    ),
                    const SizedBox(height: 24),

                    CustomButton(
                      text: _isEditingAddress ? 'Update Address' : 'Add Address',
                      onPressed: _isAddingAddress ? () {} : _addAddress,
                      isLoading: _isAddingAddress,
                      type: ButtonType.primary,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOrdersTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Orders Section
          const Text(
            'Your Orders',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color(0x1A6200EE), // AppTheme.primaryColor with 10% opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.shopping_bag, color: AppTheme.primaryColor),
              ),
              title: const Text(
                'Order History',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              subtitle: const Text('View all your past orders'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UserOrderHistoryScreen(),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 32),

          // Prescriptions Section
          const Text(
            'Prescriptions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color(0x1A6200EE), // AppTheme.primaryColor with 10% opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.description, color: AppTheme.primaryColor),
              ),
              title: const Text(
                'My Prescriptions',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              subtitle: const Text('View your uploaded prescriptions'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pushNamed(context, '/profile/prescriptions');
              },
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color(0x1A2196F3), // Colors.blue with 10% opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.upload_file, color: Colors.blue),
              ),
              title: const Text(
                'Upload Prescription',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              subtitle: const Text('Upload a new prescription'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pushNamed(context, '/profile/upload-prescription');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrescriptionsTab() {
    return const UserPrescriptionHistoryScreen();
  }
}
