import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class AnimatedAuthInput extends StatefulWidget {
  final String label;
  final String hint;
  final TextEditingController controller;
  final bool obscureText;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final IconData prefixIcon;
  final Widget? suffixIcon;
  final bool enabled;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final void Function(String)? onChanged;
  final void Function()? onTap;

  const AnimatedAuthInput({
    super.key,
    required this.label,
    required this.hint,
    required this.controller,
    required this.prefixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.suffixIcon,
    this.enabled = true,
    this.inputFormatters,
    this.focusNode,
    this.textInputAction,
    this.onChanged,
    this.onTap,
  });

  @override
  State<AnimatedAuthInput> createState() => _AnimatedAuthInputState();
}

class _AnimatedAuthInputState extends State<AnimatedAuthInput>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _focusAnimation;
  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _focusAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    widget.focusNode?.addListener(_handleFocusChange);

    // If this field has initial focus, animate immediately
    if (widget.focusNode?.hasFocus ?? false) {
      _isFocused = true;
      _controller.value = 1.0;
    }
  }

  void _handleFocusChange() {
    if (widget.focusNode?.hasFocus != _isFocused) {
      setState(() {
        _isFocused = widget.focusNode?.hasFocus ?? false;
      });
      if (_isFocused) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(_handleFocusChange);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            if (widget.label.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 4, bottom: 8),
                child: Text(
                  widget.label,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _isFocused
                        ? ScreenConstants.primaryGradientStart
                        : ScreenConstants.descriptionColor,
                  ),
                ),
              ),

            // Input field with elegant animated container
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: _hasError
                        ? ScreenConstants.accentColor.withAlpha(10)
                        : _isFocused
                            ? ScreenConstants.primaryGradientStart.withAlpha(10)
                            : Colors.black.withAlpha(5),
                    blurRadius: 3,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
                border: Border.all(
                  color: _hasError
                      ? ScreenConstants.accentColor
                      : _isFocused
                          ? ScreenConstants.primaryGradientStart
                          : Colors.grey.withAlpha(80),
                  width: _isFocused ? 2.0 : 1.5,
                ),
              ),
              child: TextFormField(
                controller: widget.controller,
                obscureText: widget.obscureText,
                keyboardType: widget.keyboardType,
                validator: (value) {
                  final error = widget.validator?.call(value);
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    setState(() {
                      _hasError = error != null;
                    });
                  });
                  return error;
                },
                enabled: widget.enabled,
                inputFormatters: widget.inputFormatters,
                focusNode: widget.focusNode,
                textInputAction: widget.textInputAction,
                onChanged: widget.onChanged,
                onTap: widget.onTap,
                style: TextStyle(
                  fontSize: 16,
                  color: ScreenConstants.titleColor,
                ),
                decoration: InputDecoration(
                  hintText: widget.hint,
                  hintStyle: TextStyle(
                    color: Colors.grey.withAlpha(150),
                    fontSize: 16,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(left: 12, right: 8),
                    child: Icon(
                      widget.prefixIcon,
                      color: _hasError
                          ? ScreenConstants.accentColor
                          : _isFocused
                              ? ScreenConstants.primaryGradientStart
                              : Colors.grey.withAlpha(150),
                      size: 22,
                    ),
                  ),
                  prefixIconConstraints: const BoxConstraints(
                    minWidth: 45,
                    minHeight: 45,
                  ),
                  suffixIcon: widget.suffixIcon,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  isDense: true,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
