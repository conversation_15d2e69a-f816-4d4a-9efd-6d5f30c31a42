import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/error_widget.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/features/admin/domain/models/prescription_model.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';

class UserPrescriptionHistoryScreen extends StatefulWidget {
  const UserPrescriptionHistoryScreen({super.key});

  @override
  State<UserPrescriptionHistoryScreen> createState() => _UserPrescriptionHistoryScreenState();
}

class _UserPrescriptionHistoryScreenState extends State<UserPrescriptionHistoryScreen> {
  String _selectedStatus = 'All';
  final List<String> _statusOptions = ['All', 'pending', 'approved', 'rejected'];

  @override
  void initState() {
    super.initState();
    _loadPrescriptions();
  }

  Future<void> _loadPrescriptions() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

    if (authProvider.isAuthenticated && authProvider.user != null) {
      await prescriptionProvider.loadUserPrescriptions(authProvider.user!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Prescriptions'),
        elevation: 0,
      ),
      body: Consumer2<AuthProvider, PrescriptionProvider>(
        builder: (context, authProvider, prescriptionProvider, child) {
          if (!authProvider.isAuthenticated) {
            return const Center(
              child: Text('Please login to view your prescriptions'),
            );
          }

          if (prescriptionProvider.status == PrescriptionStatus.loading) {
            return const LoadingIndicator(message: 'Loading prescriptions...');
          }

          if (prescriptionProvider.status == PrescriptionStatus.error) {
            return CustomErrorWidget(
              message: prescriptionProvider.errorMessage ?? 'Failed to load prescriptions',
              onRetry: _loadPrescriptions,
            );
          }

          final allPrescriptions = prescriptionProvider.prescriptions;

          if (allPrescriptions.isEmpty) {
            return _buildEmptyState();
          }

          // Filter prescriptions by status if needed
          final filteredPrescriptions = _selectedStatus == 'All'
              ? allPrescriptions
              : allPrescriptions.where((prescription) =>
                  prescription.status.toLowerCase() == _selectedStatus.toLowerCase()).toList();

          return Column(
            children: [
              // Status filter
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Filter by Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _statusOptions.map((status) {
                          final isSelected = _selectedStatus == status;
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(status),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedStatus = status;
                                });
                              },
                              backgroundColor: Colors.grey[200],
                              selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                              checkmarkColor: AppTheme.primaryColor,
                              labelStyle: TextStyle(
                                color: isSelected ? AppTheme.primaryColor : Colors.black87,
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),

              // Prescription list
              Expanded(
                child: filteredPrescriptions.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.filter_alt_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'No prescriptions with this status',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: () => setState(() => _selectedStatus = 'All'),
                              child: const Text('Show all prescriptions'),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: filteredPrescriptions.length,
                        itemBuilder: (context, index) {
                          return _buildPrescriptionCard(context, filteredPrescriptions[index]);
                        },
                      ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/profile/upload-prescription').then((_) {
            _loadPrescriptions();
          });
        },
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.add),
        label: const Text('New Prescription'),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_services_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 24),
          const Text(
            'No Prescriptions Yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Upload a prescription to get started',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          CustomButton(
            text: 'Upload Prescription',
            onPressed: () {
              Navigator.pushNamed(context, '/profile/upload-prescription').then((_) {
                _loadPrescriptions();
              });
            },
            type: ButtonType.primary,
            icon: Icons.upload_file,
          ),
        ],
      ),
    );
  }

  Widget _buildPrescriptionCard(BuildContext context, PrescriptionModel prescription) {
    final statusColor = _getStatusColor(prescription.status);
    final formattedDate = DateFormat('MMM d, yyyy - h:mm a').format(prescription.createdAt);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Prescription #${prescription.id.substring(0, 8)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    prescription.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Prescription details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      'Uploaded on $formattedDate',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Prescription image
                if (prescription.imageUrl.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      // Show full-screen image
                      showDialog(
                        context: context,
                        builder: (context) => Dialog(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AppBar(
                                title: const Text('Prescription'),
                                automaticallyImplyLeading: false,
                                actions: [
                                  IconButton(
                                    icon: const Icon(Icons.close),
                                    onPressed: () => Navigator.pop(context),
                                  ),
                                ],
                              ),
                              InteractiveViewer(
                                panEnabled: true,
                                boundaryMargin: const EdgeInsets.all(20),
                                minScale: 0.5,
                                maxScale: 4,
                                child: Image.network(
                                  _getFullImageUrl(prescription.imageUrl),
                                  fit: BoxFit.contain,
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Center(
                                      child: CircularProgressIndicator(
                                        value: loadingProgress.expectedTotalBytes != null
                                            ? loadingProgress.cumulativeBytesLoaded /
                                                loadingProgress.expectedTotalBytes!
                                            : null,
                                      ),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    print('Error loading image: $error');
                                    return Container(
                                      height: 300,
                                      color: Colors.grey[200],
                                      child: const Center(
                                        child: Icon(Icons.error, size: 50, color: Colors.grey),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    child: Container(
                      height: 150,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[200],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          _getFullImageUrl(prescription.imageUrl),
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            print('Error loading image: $error');
                            return const Center(
                              child: Icon(
                                Icons.broken_image,
                                size: 48,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),

                if (prescription.notes.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Notes:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    prescription.notes,
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Helper method to get full image URL
  String _getFullImageUrl(String imageUrl) {
    // If the URL is empty or null, return a placeholder
    if (imageUrl.isEmpty) {
      return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If it's already a Cloudinary URL, use it directly
    if (imageUrl.contains('cloudinary.com')) {
      // For Cloudinary raw URLs, convert them to image URLs if needed
      if (imageUrl.contains('/raw/upload/')) {
        return imageUrl.replaceAll('/raw/upload/', '/image/upload/');
      }
      return imageUrl;
    }

    // If it's any other full URL, use it directly
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Handle relative paths
    final baseUrl = AppConstants.baseUrl.replaceAll('/api', '');

    if (imageUrl.startsWith('/')) {
      // Path with leading slash
      return '$baseUrl$imageUrl';
    } else {
      // Path without leading slash
      return '$baseUrl/$imageUrl';
    }
  }
}
