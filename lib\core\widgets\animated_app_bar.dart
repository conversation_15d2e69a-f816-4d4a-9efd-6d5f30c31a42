import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';
import 'package:provider/provider.dart';

class AnimatedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final bool centerTitle;
  final double elevation;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Widget? leading;
  final Color? backgroundColor;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final Widget? subtitle;
  final bool isSwiggyStyle;
  final VoidCallback? onAddressPressed;
  final bool showAddressBar;

  const AnimatedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.bottom,
    this.centerTitle = false, // <PERSON>wiggy uses left-aligned titles
    this.elevation = 0,
    this.showBackButton = true,
    this.onBackPressed,
    this.leading,
    this.backgroundColor,
    this.systemOverlayStyle,
    this.subtitle,
    this.isSwiggyStyle = true,
    this.onAddressPressed,
    this.showAddressBar = false, // Only show address bar on home page
  });

  @override
  Widget build(BuildContext context) {
    // Define colors - Using centralized AppTheme
    const Color appBarTextColor = Color(0xFF333333);
    const Color borderColor = Color(0xFFEEEEEE);
    final Color iconColor = AppTheme.primaryColor;

    if (isSwiggyStyle) {
      // App bar based on the screenshots
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: borderColor,
              width: 1.0,
            ),
          ),
        ),
        child: AppBar(
          centerTitle: false, // Left-align all titles
          titleSpacing: showAddressBar ? 0 : 16.0, // Consistent left padding
          title: showAddressBar
            // Address bar for home page
            ? Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Row(
                  children: [
                    // Location icon and dropdown section
                    Expanded(
                      child: GestureDetector(
                        onTap: onAddressPressed,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: iconColor,
                                  size: 18,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  title,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: appBarTextColor,
                                    letterSpacing: 0.2,
                                    fontFamily: 'Poppins',
                                  ),
                                ),
                                Icon(
                                  Icons.keyboard_arrow_down,
                                  color: iconColor,
                                  size: 18,
                                ),
                              ],
                            ),
                            if (subtitle != null)
                              subtitle!
                            else
                              Consumer<AddressProvider>(
                                builder: (context, addressProvider, child) {
                                  if (addressProvider.hasAddresses && addressProvider.selectedAddress != null) {
                                    final selectedAddress = addressProvider.selectedAddress!;
                                    final formattedAddress = '${selectedAddress.address}, ${selectedAddress.city}, ${selectedAddress.pincode}';
                                    return Padding(
                                      padding: const EdgeInsets.only(left: 22.0),
                                      child: Text(
                                        formattedAddress,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF686B78),
                                          fontWeight: FontWeight.w300,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    );
                                  }

                                  return Padding(
                                    padding: const EdgeInsets.only(left: 22.0),
                                    child: Text(
                                      'Tap to select delivery address',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF686B78),
                                        fontWeight: FontWeight.w300,
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )
            // Regular title for other pages
            : Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: appBarTextColor,
                  letterSpacing: 0.2,
                  fontFamily: 'Poppins',
                ),
              ),
          // Wrap actions with a theme to apply consistent icon styling
          actions: actions != null ?
            <Widget>[
              Theme(
                data: Theme.of(context).copyWith(
                  iconTheme: IconThemeData(
                    color: iconColor,
                    size: 24,
                  ),
                ),
                child: Row(children: actions!),
              ),
            ] : null,
          bottom: bottom,
          elevation: 0,
          backgroundColor: Colors.white,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          automaticallyImplyLeading: showBackButton,
          iconTheme: IconThemeData(color: iconColor, size: 24),
          leading: leading ?? (showBackButton
              ? IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: iconColor,
                    size: 24,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                )
              : null),
        ),
      );
    } else {
      // Simple app bar style based on screenshots
      final Color iconColor = AppTheme.primaryColor;

      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFEEEEEE),
              width: 1.0,
            ),
          ),
        ),
        child: AppBar(
          titleSpacing: 16.0, // Consistent left padding
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Color(0xFF333333),
              letterSpacing: 0.2,
              fontFamily: 'Poppins',
            ),
          ),
          // Wrap actions with a theme to apply consistent icon styling
          actions: actions != null ?
            <Widget>[
              Theme(
                data: Theme.of(context).copyWith(
                  iconTheme: IconThemeData(
                    color: iconColor,
                    size: 24,
                  ),
                ),
                child: Row(children: actions!),
              ),
            ] : null,
          bottom: bottom,
          centerTitle: false, // Left-align all titles
          elevation: 0,
          backgroundColor: Colors.white,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          automaticallyImplyLeading: showBackButton,
          iconTheme: IconThemeData(color: iconColor, size: 24),
          leading: leading ?? (showBackButton
              ? IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: iconColor,
                    size: 24,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                )
              : null),
        ),
      );
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(
      kToolbarHeight + (bottom?.preferredSize.height ?? 0.0));
}

class BottomNavAppBar extends AnimatedAppBar {
  /// Creates a bottom navigation app bar with dynamic address support from the user's profile.
  ///
  /// The address is automatically loaded from the AddressProvider, which contains
  /// the user's selected address from their profile.
  BottomNavAppBar({
    super.key,
    super.title = 'Medicine Shop',
    String? address,
    Future<String?>? futureAddress,
    Stream<String?>? addressStream,
    super.actions,
    super.bottom,
    super.elevation = 0,
    super.backgroundColor,
    Widget? customSubtitle,
    super.onAddressPressed,
    super.showAddressBar = false, // Only show address on home page
    super.centerTitle = false, // Left-align all titles
  }) : super(
          showBackButton: false,
          isSwiggyStyle: true,
          subtitle: customSubtitle ??
                    (address != null ?
                      _buildDefaultSubtitle(address) :
                      _buildDynamicAddressSubtitle(futureAddress, addressStream)),
        );

  // Default subtitle widget with static address details
  static Widget _buildDefaultSubtitle(String address) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0),
      child: Text(
        address,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF686B78), // Swiggy light grey
          fontWeight: FontWeight.w300,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // Dynamic address subtitle that loads from AddressProvider
  static Widget _buildDynamicAddressSubtitle(
    Future<String?>? futureAddress,
    Stream<String?>? addressStream,
  ) {
    return Consumer2<AuthProvider, AddressProvider>(
      builder: (context, authProvider, addressProvider, child) {
        // Check address status and availability

        // Check if we have a selected address from the AddressProvider
        if (addressProvider.hasAddresses && addressProvider.selectedAddress != null) {
          final selectedAddress = addressProvider.selectedAddress!;
          // Format the address as it appears in the profile page
          final formattedAddress = '${selectedAddress.address}, ${selectedAddress.city}, ${selectedAddress.pincode}';
          // Display the formatted address
          return _buildDefaultSubtitle(formattedAddress);
        }

        // If addresses aren't loaded yet but user is authenticated, load them
        if (authProvider.isAuthenticated &&
            authProvider.user != null &&
            addressProvider.status != AddressStatus.loading) {
          // Load addresses in the next frame to avoid build-during-build errors
          final user = authProvider.user;
          if (user != null) {
            // Use addPostFrameCallback instead of microtask for more reliable execution
            WidgetsBinding.instance.addPostFrameCallback((_) {
              addressProvider.loadAddresses(user);
            });
          }
          return _buildLoadingAddressSubtitle();
        }

        // If addresses are currently loading, show loading state
        if (addressProvider.status == AddressStatus.loading) {
          // Show loading indicator while addresses are being fetched
          return _buildLoadingAddressSubtitle();
        }

        // If we have a stream, use StreamBuilder as fallback
        if (addressStream != null) {
          return StreamBuilder<String?>(
            stream: addressStream,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting && !snapshot.hasData) {
                return _buildLoadingAddressSubtitle();
              }

              final address = snapshot.data;
              if (address == null || address.isEmpty) {
                return _buildNoAddressSubtitle();
              }

              return _buildDefaultSubtitle(address);
            },
          );
        }

        // If we have a future, use FutureBuilder as fallback
        if (futureAddress != null) {
          return FutureBuilder<String?>(
            future: futureAddress,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildLoadingAddressSubtitle();
              }

              final address = snapshot.data;
              if (address == null || address.isEmpty) {
                return _buildNoAddressSubtitle();
              }

              return _buildDefaultSubtitle(address);
            },
          );
        }

        // If no address source is provided and no address in provider, show a default message
        return _buildNoAddressSubtitle();
      },
    );
  }

  // Loading state for address
  static Widget _buildLoadingAddressSubtitle() {
    return const Padding(
      padding: EdgeInsets.only(left: 22.0),
      child: Text(
        'Loading address...',
        style: TextStyle(
          fontSize: 12,
          color: Color(0xFF686B78), // Swiggy light grey
          fontWeight: FontWeight.w300,
          fontStyle: FontStyle.italic,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // No address state - shows a prompt to add an address
  static Widget _buildNoAddressSubtitle() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If user is not authenticated, show a different message
        if (!authProvider.isAuthenticated) {
          return const Padding(
            padding: EdgeInsets.only(left: 22.0),
            child: Text(
              'Please log in to set delivery address',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontWeight: FontWeight.w300,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }

        return GestureDetector(
          onTap: () {
            // Navigate to the profile page's address tab
            Navigator.pushNamed(context, '/profile', arguments: {'initialTab': 1});
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 22.0),
            child: Row(
              children: [
                Text(
                  'Add delivery address',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.add_circle_outline,
                  size: 14,
                  color: AppTheme.primaryColor,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// A search bar widget for medicine shop application
class SwiggySearchBar extends StatelessWidget {
  final String hintText;
  final VoidCallback? onTap;
  final bool showFilterButton;
  final VoidCallback? onFilterTap;

  const SwiggySearchBar({
    super.key,
    this.hintText = 'Search for medicines',
    this.onTap,
    this.showFilterButton = true,
    this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          // Search bar
          Expanded(
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                height: 48,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x08000000), // 3% opacity black
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.search,
                      color: Colors.grey,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      hintText,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.mic,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Filter button
          if (showFilterButton)
            Padding(
              padding: const EdgeInsets.only(left: 12.0),
              child: Container(
                height: 48,
                width: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x08000000), // 3% opacity black
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: onFilterTap,
                    child: Center(
                      child: Icon(
                        Icons.filter_list,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// A horizontal category scroller widget for medicine categories
class SwiggyCategoryScroller extends StatelessWidget {
  final List<MedicineCategory> categories;
  final ValueChanged<MedicineCategory>? onCategoryTap;
  final int selectedIndex;

  const SwiggyCategoryScroller({
    super.key,
    required this.categories,
    this.onCategoryTap,
    this.selectedIndex = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = index == selectedIndex;

          return GestureDetector(
            onTap: () {
              if (onCategoryTap != null) {
                onCategoryTap!(category);
              }
            },
            child: Container(
              width: 70,
              margin: const EdgeInsets.only(right: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Category image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x08000000),
                          blurRadius: 4,
                          spreadRadius: 0,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: category.iconData != null
                          ? Icon(
                              category.iconData,
                              size: 30,
                              color: isSelected
                                  ? AppTheme.primaryColor
                                  : Colors.grey.shade700,
                            )
                          : Image.asset(
                              category.imageAsset ?? 'assets/images/medicine_default.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(height: 6),
                  // Category name
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? AppTheme.primaryColor : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Model class for medicine categories
class MedicineCategory {
  final String name;
  final String? imageAsset;
  final IconData? iconData;

  const MedicineCategory({
    required this.name,
    this.imageAsset,
    this.iconData,
  }) : assert(imageAsset != null || iconData != null,
       'Either imageAsset or iconData must be provided');
}

/// A promotional banner widget for medicine shop offers
class SwiggyPromotionalBanner extends StatelessWidget {
  final String title;
  final String subtitle;
  final String imageAsset;
  final String buttonText;
  final VoidCallback? onButtonTap;

  const SwiggyPromotionalBanner({
    super.key,
    required this.title,
    required this.subtitle,
    required this.imageAsset,
    this.buttonText = 'ORDER NOW',
    this.onButtonTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withAlpha(230), // primaryGradientStart with 90% opacity
            AppTheme.secondaryColor.withAlpha(230), // primaryGradientEnd with 90% opacity
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withAlpha(40), // primaryGradientStart with 16% opacity
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Text content
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Order now button
                  GestureDetector(
                    onTap: onButtonTap,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        buttonText,
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Image
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Image.asset(
                imageAsset,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A medicine product card widget
class MedicineProductCard extends StatelessWidget {
  final String name;
  final String imageAsset;
  final double rating;
  final String manufacturer;
  final String category;
  final double price;
  final double? originalPrice;
  final bool isPrescriptionRequired;
  final bool isFavorite;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteTap;
  final VoidCallback? onAddToCartTap;

  const MedicineProductCard({
    super.key,
    required this.name,
    required this.imageAsset,
    required this.rating,
    required this.manufacturer,
    required this.category,
    required this.price,
    this.originalPrice,
    this.isPrescriptionRequired = false,
    this.isFavorite = false,
    this.onTap,
    this.onFavoriteTap,
    this.onAddToCartTap,
  });

  @override
  Widget build(BuildContext context) {
    final discountPercentage = originalPrice != null
        ? ((originalPrice! - price) / originalPrice! * 100).round()
        : 0;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: Color(0x08000000),
              blurRadius: 8,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Medicine image with favorite button and discount tag
            Stack(
              children: [
                // Medicine image
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  child: Image.asset(
                    imageAsset,
                    height: 160,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),

                // Favorite button
                Positioned(
                  top: 12,
                  right: 12,
                  child: GestureDetector(
                    onTap: onFavoriteTap,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey,
                        size: 20,
                      ),
                    ),
                  ),
                ),

                // Discount tag
                if (originalPrice != null && discountPercentage > 0)
                  Positioned(
                    bottom: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: ScreenConstants.accentColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '$discountPercentage% OFF',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                // Prescription Required tag
                if (isPrescriptionRequired)
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.receipt,
                            color: Colors.white,
                            size: 12,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Rx',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            // Medicine details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Medicine name
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Manufacturer
                  Text(
                    manufacturer,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Rating row
                  Row(
                    children: [
                      // Rating
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Text(
                              rating.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 2),
                            const Icon(
                              Icons.star,
                              color: Colors.white,
                              size: 10,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),

                      // Category
                      Expanded(
                        child: Text(
                          category,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Price and add to cart row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (originalPrice != null)
                            Text(
                              '₹${originalPrice!.toStringAsFixed(2)}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          Text(
                            '₹${price.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                      // Add to cart button
                      GestureDetector(
                        onTap: onAddToCartTap,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.primaryColor,
                                AppTheme.secondaryColor,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Row(
                            children: [
                              Icon(
                                Icons.add_shopping_cart,
                                color: Colors.white,
                                size: 16,
                              ),
                              SizedBox(width: 4),
                              Text(
                                'ADD',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}