import 'package:flutter/foundation.dart';
import 'package:medicine_shop/features/auth/data/auth_service.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  authenticating,
  error,
}

class AuthProvider with ChangeNotifier {
  final AuthService _authService;

  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;

  AuthProvider({
    AuthService? authService,
  }) : _authService = authService ?? AuthService() {
    // Check if user is already logged in
    _checkCurrentUser();
  }

  // Getters
  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isAdmin => _user?.isAdmin ?? false;

  // Check current user
  Future<void> _checkCurrentUser() async {
    _status = AuthStatus.authenticating;
    notifyListeners();

    try {
      // First try to get user from local storage for quick UI display
      final storedUser = await _authService.getUserFromStorage();
      if (storedUser != null) {
        _user = storedUser;
        _status = AuthStatus.authenticated;
        notifyListeners();
      }

      // Then verify with the server
      // If there's a network error, getCurrentUser will return the stored user
      // instead of null, so we don't log the user out unnecessarily
      final user = await _authService.getCurrentUser();

      if (user != null) {
        _user = user;
        _status = AuthStatus.authenticated;
      } else if (storedUser == null) {
        // Only set to unauthenticated if we don't have a stored user
        // This prevents logging out on network errors
        _user = null;
        _status = AuthStatus.unauthenticated;
      } else {
        // If we had a stored user but server verification failed,
        // keep using the stored user (for offline support)
        _user = storedUser;
        _status = AuthStatus.authenticated;
      }
    } catch (e) {
      // Don't change status to error if we already have a stored user
      // This allows the app to work offline with cached user data
      if (_user == null) {
        _status = AuthStatus.error;
        _errorMessage = e.toString();
      }
    }

    notifyListeners();
  }

  // Register
  Future<bool> register(String userName, String password, {String? phoneNumber, required String email, String role = 'user'}) async {
    try {
      final success = await _authService.register(
        userName,
        password,
        phoneNumber: phoneNumber,
        email: email,
        role: role
      );
      return success;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _status = AuthStatus.authenticating;
    _errorMessage = null;
    notifyListeners();

    try {
      final user = await _authService.login(email, password);
      _user = user;
      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _status = AuthStatus.error;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _status = AuthStatus.authenticating;
    notifyListeners();

    try {
      await _authService.logout();
      _user = null;
      _status = AuthStatus.unauthenticated;
    } catch (e) {
      _status = AuthStatus.error;
      _errorMessage = e.toString();
    }

    notifyListeners();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
