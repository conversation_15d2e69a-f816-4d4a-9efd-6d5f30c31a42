import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/features/products/domain/models/product_model.dart';
import 'package:medicine_shop/features/products/presentation/providers/product_provider.dart';
import 'package:medicine_shop/features/products/presentation/widgets/product_card.dart';

class RelatedProducts extends StatelessWidget {
  final ProductModel currentProduct;
  final Function(ProductModel) onProductTap;
  final Function(ProductModel) onAddToCart;

  const RelatedProducts({
    super.key,
    required this.currentProduct,
    required this.onProductTap,
    required this.onAddToCart,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<ProductModel>>(
      future: _getRelatedProducts(context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            height: 200,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Finding similar products...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.hasError) {
          debugPrint('Error in related products: ${snapshot.error}');
          return const SizedBox.shrink();
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          // Return a placeholder when no related products are found
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.category_outlined, size: 40, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    'No related products found',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final relatedProducts = snapshot.data!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Related Products',
                    style: AppTheme.subheadingStyle,
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to category page
                      Provider.of<ProductProvider>(context, listen: false)
                          .updateFilters(categories: [currentProduct.category]);
                      Navigator.pop(context);
                    },
                    child: Text(
                      'View All',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: relatedProducts.length,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemBuilder: (context, index) {
                  final product = relatedProducts[index];
                  return SizedBox(
                    width: 180,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: ProductCard(
                        product: product,
                        onTap: () => onProductTap(product),
                        onAddToCart: () => onAddToCart(product),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Future<List<ProductModel>> _getRelatedProducts(BuildContext context) async {
    // Get the provider immediately to avoid BuildContext across async gap
    final productProvider = Provider.of<ProductProvider>(context, listen: false);

    try {
      // Add a timeout to prevent infinite loading
      return await Future.delayed(const Duration(seconds: 3), () async {
        // Get products in the same category
        await productProvider.loadProductsByCategory(currentProduct.category);

        // Filter out the current product and limit to 5 products
        final relatedProducts = productProvider.products
            .where((product) => product.id != currentProduct.id)
            .take(5)
            .toList();

        return relatedProducts;
      }).timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          // Return empty list on timeout to avoid showing loading spinner forever
          debugPrint('Timeout getting related products');
          return [];
        },
      );
    } catch (e) {
      debugPrint('Error getting related products: $e');
      // Return empty list on error to avoid showing loading spinner forever
      return [];
    }
  }
}
