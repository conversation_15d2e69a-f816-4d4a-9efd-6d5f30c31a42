import 'dart:math';
import 'package:flutter/material.dart';
import 'package:medicine_shop/core/constants/screen_constants.dart';

class AnimatedIllustration extends StatefulWidget {
  final String title;
  final IconData icon;

  const AnimatedIllustration({
    Key? key,
    required this.title,
    required this.icon,
  }) : super(key: key);

  @override
  State<AnimatedIllustration> createState() => _AnimatedIllustrationState();
}

class _AnimatedIllustrationState extends State<AnimatedIllustration>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _rotateAnimation = Tween<double>(begin: -0.05, end: 0.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use consistent colors for all illustrations (matching splash screen)
    final Color primaryColor = ScreenConstants.primaryGradientStart;
    final Color secondaryColor = ScreenConstants.primaryGradientEnd;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotateAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Background glow
                Container(
                  width: 160,
                  height: 160,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        primaryColor.withAlpha((0.3 * _opacityAnimation.value * 255).toInt()),
                        primaryColor.withAlpha((0.1 * _opacityAnimation.value * 255).toInt()),
                        Colors.transparent,
                      ],
                      stops: const [0.4, 0.7, 1.0],
                    ),
                  ),
                ),

                // Main container with icon - improved for clarity
                Container(
                  width: 130,
                  height: 130,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        primaryColor,
                        secondaryColor,
                      ],
                      stops: const [0.2, 0.9],
                    ),
                    borderRadius: BorderRadius.circular(35),
                    boxShadow: [
                      // Inner shadow for depth
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 1,
                        spreadRadius: 0,
                        offset: const Offset(0, 1),
                      ),
                      // Outer glow
                      BoxShadow(
                        color: primaryColor.withAlpha((0.4 * _opacityAnimation.value * 255).toInt()),
                        blurRadius: 15,
                        spreadRadius: 0,
                        offset: const Offset(0, 8),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withAlpha((0.25 * _opacityAnimation.value * 255).toInt()),
                      width: 2.0,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(33),
                    child: Center(
                      child: _buildIconWithEffects(widget.icon),
                    ),
                  ),
                ),

                // Decorative elements
                ..._buildDecorativeElements(primaryColor, secondaryColor),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildIconWithEffects(IconData icon) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Center(
        child: Icon(
          icon,
          size: 60,
          color: Colors.white,
          // Use anti-aliasing for sharper rendering
          textDirection: TextDirection.ltr,
          semanticLabel: 'Onboarding icon',
        ),
      ),
    );
  }

  List<Widget> _buildDecorativeElements(Color primaryColor, Color secondaryColor) {
    final List<Widget> elements = [];

    // Add floating dots/elements around the main container
    for (int i = 0; i < 5; i++) {
      final angle = i * (pi / 2.5) + (_controller.value * pi);
      final distance = 80.0 + (sin(_controller.value * pi * 2 + i) * 10);
      final size = 6.0 + (sin(_controller.value * pi * 2 + i) * 3);

      final x = cos(angle) * distance;
      final y = sin(angle) * distance;

      final color = i % 2 == 0 ? primaryColor : secondaryColor;

      elements.add(
        Positioned(
          left: x + 65,
          top: y + 65,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color.withAlpha((0.7 * _opacityAnimation.value * 255).toInt()),
              shape: i % 3 == 0 ? BoxShape.circle : BoxShape.rectangle,
              borderRadius: i % 3 == 0 ? null : BorderRadius.circular(2),
              boxShadow: [
                BoxShadow(
                  color: color.withAlpha((0.3 * _opacityAnimation.value * 255).toInt()),
                  blurRadius: 5,
                  spreadRadius: 0,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return elements;
  }
}
