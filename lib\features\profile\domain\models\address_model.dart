class AddressModel {
  final String id;
  final String userId;
  final String address;
  final String city;
  final String pincode;
  final String phone;
  final String notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AddressModel({
    required this.id,
    required this.userId,
    required this.address,
    required this.city,
    required this.pincode,
    required this.phone,
    required this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      pincode: json['pincode'] ?? '',
      phone: json['phone'] ?? '',
      notes: json['notes'] ?? '',
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'address': address,
      'city': city,
      'pincode': pincode,
      'phone': phone,
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // For creating a new address
  Map<String, dynamic> toCreateJson() {
    return {
      'userId': userId,
      'address': address,
      'city': city,
      'pincode': pincode,
      'phone': phone,
      'notes': notes,
    };
  }

  // For updating an existing address
  Map<String, dynamic> toUpdateJson() {
    return {
      'address': address,
      'city': city,
      'pincode': pincode,
      'phone': phone,
      'notes': notes,
    };
  }

  // Create a copy with updated fields
  AddressModel copyWith({
    String? id,
    String? userId,
    String? address,
    String? city,
    String? pincode,
    String? phone,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      address: address ?? this.address,
      city: city ?? this.city,
      pincode: pincode ?? this.pincode,
      phone: phone ?? this.phone,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
