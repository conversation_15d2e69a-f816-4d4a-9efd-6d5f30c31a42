import 'package:medicine_shop/core/constants/app_constants.dart';
import 'package:medicine_shop/core/network/api_service.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';

class UserService {
  final ApiService _apiService;

  UserService({ApiService? apiService}) : _apiService = apiService ?? ApiService();

  // Get all users (for admin)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.baseUrl}/api/admin/users',
      );

      if (response['success'] == true) {
        final List<dynamic> usersJson = response['data'];
        return usersJson.map((json) => UserModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get users');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get user by ID
  Future<UserModel> getUserById(String userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.baseUrl}/api/admin/users/$userId',
      );

      if (response['success'] == true) {
        return UserModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get user');
      }
    } catch (e) {
      rethrow;
    }
  }
}
