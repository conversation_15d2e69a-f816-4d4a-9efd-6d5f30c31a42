import 'package:flutter/foundation.dart';
import 'package:medicine_shop/features/admin/data/user_service.dart';
import 'package:medicine_shop/features/auth/domain/models/user_model.dart';

enum UserStatus {
  initial,
  loading,
  loaded,
  error,
}

class UserProvider with ChangeNotifier {
  final UserService _userService;
  
  UserStatus _status = UserStatus.initial;
  List<UserModel> _users = [];
  UserModel? _selectedUser;
  String? _errorMessage;
  
  UserProvider({
    UserService? userService,
  }) : _userService = userService ?? UserService();
  
  // Getters
  UserStatus get status => _status;
  List<UserModel> get users => _users;
  UserModel? get selectedUser => _selectedUser;
  String? get errorMessage => _errorMessage;
  
  // Load all users (for admin)
  Future<void> loadAllUsers() async {
    _status = UserStatus.loading;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final users = await _userService.getAllUsers();
      _users = users;
      _status = UserStatus.loaded;
    } catch (e) {
      _status = UserStatus.error;
      _errorMessage = e.toString();
    }
    
    notifyListeners();
  }
  
  // Load user by ID
  Future<void> loadUserById(String userId) async {
    _status = UserStatus.loading;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final user = await _userService.getUserById(userId);
      _selectedUser = user;
      _status = UserStatus.loaded;
    } catch (e) {
      _status = UserStatus.error;
      _errorMessage = e.toString();
    }
    
    notifyListeners();
  }
  
  // Select a user
  void selectUser(String userId) {
    final index = _users.indexWhere((u) => u.id == userId);
    if (index >= 0) {
      _selectedUser = _users[index];
      notifyListeners();
    }
  }
  
  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
