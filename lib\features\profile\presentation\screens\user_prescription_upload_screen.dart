import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:medicine_shop/core/theme/app_theme.dart';
import 'package:medicine_shop/core/widgets/custom_button.dart';
import 'package:medicine_shop/core/widgets/loading_indicator.dart';
import 'package:medicine_shop/features/admin/presentation/providers/prescription_provider.dart';
import 'package:medicine_shop/features/auth/presentation/providers/auth_provider.dart';
import 'package:medicine_shop/features/profile/domain/models/address_model.dart';
import 'package:medicine_shop/features/profile/presentation/providers/address_provider.dart';

// Helper class to store web image bytes
class WebImageBytes {
  static Uint8List? imageBytes;
  static String? filename;

  static void clear() {
    imageBytes = null;
    filename = null;
  }
}

class UserPrescriptionUploadScreen extends StatefulWidget {
  const UserPrescriptionUploadScreen({super.key});

  @override
  State<UserPrescriptionUploadScreen> createState() => _UserPrescriptionUploadScreenState();
}

class _UserPrescriptionUploadScreenState extends State<UserPrescriptionUploadScreen> {
  File? _imageFile;
  Uint8List? _imageBytes;
  final _notesController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  AddressModel? _selectedAddress;
  bool _addressesLoaded = false;

  @override
  void initState() {
    super.initState();
    // Load user addresses when the screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserAddresses();
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  // Load user addresses
  Future<void> _loadUserAddresses() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final addressProvider = Provider.of<AddressProvider>(context, listen: false);

    if (authProvider.isAuthenticated && authProvider.user != null) {
      await addressProvider.loadAddresses(authProvider.user!);
      setState(() {
        _addressesLoaded = true;
        _selectedAddress = addressProvider.selectedAddress;
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          // For web platform
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _imageBytes = bytes;
            _errorMessage = null;
          });
        } else {
          // For mobile platforms
          setState(() {
            _imageFile = File(pickedFile.path);
            _errorMessage = null;
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pick image: $e';
      });
    }
  }

  Future<void> _uploadPrescription() async {
    // Check if image is selected
    bool hasImage = kIsWeb ? _imageBytes != null : _imageFile != null;
    if (!hasImage) {
      setState(() {
        _errorMessage = 'Please select a prescription image';
      });
      return;
    }

    // Check if address is selected
    if (_selectedAddress == null) {
      setState(() {
        _errorMessage = 'Please select a delivery address';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final prescriptionProvider = Provider.of<PrescriptionProvider>(context, listen: false);

      if (authProvider.user == null) {
        throw Exception('User not authenticated');
      }

      print('Starting prescription upload process');
      print('User: ${authProvider.user!.userName}, ID: ${authProvider.user!.id}');
      print('Address: ${_selectedAddress!.address}, City: ${_selectedAddress!.city}');

      File imageFileToUpload;

      // Prepare the image file
      if (kIsWeb && _imageBytes != null) {
        print('Creating file from bytes for web');
        imageFileToUpload = await _createFileFromBytes();
      } else if (_imageFile != null) {
        print('Using existing file for mobile: ${_imageFile!.path}');
        imageFileToUpload = _imageFile!;
      } else {
        throw Exception('No image file available');
      }

      // Use the direct upload endpoint with the selected address information
      final success = await prescriptionProvider.uploadPrescriptionDirect(
        imageFileToUpload,
        authProvider.user!,
        '', // No admin assigned yet
        _notesController.text.trim(),
        // Add address information from selected address
        addressId: _selectedAddress!.id,
        address: _selectedAddress!.address,
        city: _selectedAddress!.city,
        pincode: _selectedAddress!.pincode,
        phone: _selectedAddress!.phone,
      );

      if (!mounted) return;

      if (success) {
        print('Prescription uploaded successfully');

        // Clear form and web image bytes
        setState(() {
          _imageFile = null;
          _imageBytes = null;
          _notesController.clear();
        });

        // Clear the static WebImageBytes
        WebImageBytes.clear();

        // Show success dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Success'),
              content: const Text('Prescription uploaded successfully!'),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Close the dialog and navigate back to the prescriptions screen
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(); // Go back to prescriptions screen
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      } else {
        // Show error from provider
        setState(() {
          _errorMessage = prescriptionProvider.errorMessage ?? 'Failed to upload prescription';
        });
        print('Upload failed: $_errorMessage');
      }
    } catch (e) {
      print('Exception during upload: $e');
      setState(() {
        _errorMessage = 'Failed to upload prescription: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Prescription'),
      ),
      body: _isLoading
          ? const LoadingIndicator(message: 'Uploading prescription...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Instructions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(25),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.withAlpha(75)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          'Upload Your Prescription',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          '1. Take a clear photo of your prescription',
                          style: TextStyle(fontSize: 14),
                        ),
                        Text(
                          '2. Make sure all details are clearly visible',
                          style: TextStyle(fontSize: 14),
                        ),
                        Text(
                          '3. Add any special instructions in the notes',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Image Preview
                  Center(
                    child: GestureDetector(
                      onTap: () => _showImageSourceDialog(),
                      child: Container(
                        width: double.infinity,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: _hasImage()
                            ? _buildImagePreview()
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: const [
                                  Icon(
                                    Icons.add_a_photo,
                                    size: 48,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Tap to add prescription image',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Image Selection Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomButton(
                        text: 'Take Photo',
                        onPressed: () => _pickImage(ImageSource.camera),
                        type: ButtonType.secondary,
                        icon: Icons.camera_alt,
                        isFullWidth: false,
                      ),
                      const SizedBox(width: 16),
                      CustomButton(
                        text: 'Choose from Gallery',
                        onPressed: () => _pickImage(ImageSource.gallery),
                        type: ButtonType.secondary,
                        icon: Icons.photo_library,
                        isFullWidth: false,
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Address Selection
                  const Text(
                    'Delivery Address',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildAddressSelector(),
                  const SizedBox(height: 24),

                  // Notes Field
                  const Text(
                    'Additional Notes',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _notesController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Add any special instructions or notes...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Error Message
                  if (_errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[100]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 24),

                  // Submit Button
                  CustomButton(
                    text: 'Upload Prescription',
                    onPressed: _uploadPrescription,
                    type: ButtonType.primary,
                    icon: Icons.upload_file,
                    isFullWidth: true,
                  ),
                ],
              ),
            ),
    );
  }

  // Check if we have an image
  bool _hasImage() {
    return kIsWeb ? _imageBytes != null : _imageFile != null;
  }

  // Build the image preview widget
  Widget _buildImagePreview() {
    if (kIsWeb && _imageBytes != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.memory(
          _imageBytes!,
          fit: BoxFit.cover,
        ),
      );
    } else if (!kIsWeb && _imageFile != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.file(
          _imageFile!,
          fit: BoxFit.cover,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  // Create a temporary file from bytes for web
  Future<File> _createFileFromBytes() async {
    if (!kIsWeb || _imageBytes == null) {
      throw Exception('Cannot create file from null bytes');
    }

    try {
      // This is a workaround for web - we're not actually creating a file
      // but we need to return a File object for the API call
      // The actual bytes will be handled in the PrescriptionService
      final file = File('prescription_${DateTime.now().millisecondsSinceEpoch}.jpg');

      // Store the bytes in a static property that can be accessed by the service
      WebImageBytes.imageBytes = _imageBytes;
      WebImageBytes.filename = file.path;

      print('Prepared web image bytes: ${_imageBytes!.length} bytes');
      return file;
    } catch (e) {
      print('Error preparing web image: $e');
      rethrow;
    }
  }

  // Build the address selector widget
  Widget _buildAddressSelector() {
    return Consumer2<AuthProvider, AddressProvider>(
      builder: (context, authProvider, addressProvider, child) {
        if (!authProvider.isAuthenticated) {
          return const Text('Please login to continue');
        }

        if (addressProvider.status == AddressStatus.loading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (addressProvider.status == AddressStatus.error) {
          return Column(
            children: [
              Text(
                'Error loading addresses: ${addressProvider.errorMessage}',
                style: const TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _loadUserAddresses,
                child: const Text('Retry'),
              ),
            ],
          );
        }

        final addresses = addressProvider.addresses;

        if (addresses.isEmpty) {
          return Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange),
                ),
                child: Column(
                  children: [
                    const Text(
                      'No addresses found',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Please add a delivery address to continue',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // Navigate to add address screen
                        Navigator.pushNamed(context, '/profile/addresses/add').then((_) {
                          _loadUserAddresses();
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Add Address'),
                    ),
                  ],
                ),
              ),
            ],
          );
        }

        return Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: addresses.length,
                itemBuilder: (context, index) {
                  final address = addresses[index];
                  final isSelected = _selectedAddress?.id == address.id;

                  return RadioListTile<String>(
                    title: Text(
                      address.address,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      '${address.city}, ${address.pincode}\nPhone: ${address.phone}',
                    ),
                    value: address.id,
                    groupValue: _selectedAddress?.id,
                    onChanged: (value) {
                      setState(() {
                        _selectedAddress = address;
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                    selected: isSelected,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: () {
                // Navigate to add address screen
                Navigator.pushNamed(context, '/profile/addresses/add').then((_) {
                  _loadUserAddresses();
                });
              },
              icon: const Icon(Icons.add),
              label: const Text('Add New Address'),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ],
        );
      },
    );
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Image Source'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
